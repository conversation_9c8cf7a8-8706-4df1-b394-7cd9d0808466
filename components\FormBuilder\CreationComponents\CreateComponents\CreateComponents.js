import React, { createElement, useState, useEffect, useContext } from 'react'
import { Sortable } from '../../../FormBuilder/DndComponents/Sortable/Sortable'
import { Resizable } from 're-resizable'
import { InputText } from 'primereact/inputtext'
import { Calendar } from 'primereact/calendar'
import { InputNumber } from 'primereact/inputnumber'
import { InputTextarea } from 'primereact/inputtextarea'
import { InputMask } from 'primereact/inputmask'
import { Dropdown } from 'primereact/dropdown'
import { MultiSelect } from 'primereact/multiselect'
import { Checkbox } from 'primereact/checkbox'
import { AutoComplete } from 'primereact/autocomplete'
import { Rating } from 'primereact/rating'
import { RadioButtons } from '../../../UI/RadioButtons/RadioButtons'
import { FileInput } from '../../SharedComponents/File/FileUpload/FileUpload'
import { NumberScale } from '../../Settings/UI/NumberScale/NumberScale'
import { ViewAddress } from '../../ViewComponents/ViewComponents/ViewComponents'
import { defaultSubtitle } from '../../DndComponents/ComponentPanel/ComponentPanel'
import { getFormSubmissionsFiltered } from '../../../../api/apiCalls'
import NextImage from 'next/image'
import clsx from 'clsx'
import Label from '../../SharedComponents/Label/Label'
import Subtitle from '../../SharedComponents/Subtitle/Subtitle'
import TableComponent from '../../SharedComponents/TableComponent/TableComponent'
import SettingsButton from '../SettingsButton/SettingsButton'
import LabelContainer from '../../SharedComponents/LabelContainer/LabelContainer'
import InputsContainer from '../../SharedComponents/InputsContainer/InputsContainer'
import AlignmentContainer from '../../SharedComponents/AlignmentContainer/AlignmentContainer'
import ComponentContainer from '../../SharedComponents/ComponentContainer/ComponentContainer'
import LexicalEditor from '../../../LexicalEditor/LexicalEditor'
import ReadonlyLexicalEditor from '../../../LexicalEditor/ReadonlyLexicalEditor/ReadonlyLexicalEditor'
import UserProfileContext from '../../../../public/UserProfileContext/UserProfileContext'
import SecondaryButton from '../../../UI/SecondaryButton/SecondaryButton'
import CheckBox from '../../../UI/Input/CheckBox/CheckBox'
import CustomFileInput from '../../../UI/Input/FileInput/FileInput'
import UrlFileInput from '../../../UI/Input/FileInput/UrlFileInput'
import styles from '../../../../styles/Inputs/Inputs.module.css'
import calendarStyles from '../../SharedComponents/Calendar/Calendar.module.css'
import numberStyles from '../../SharedComponents/Number/Number.module.css'
import textareaStyles from '../../SharedComponents/Textarea/Textarea.module.css'
import maskStyles from '../../SharedComponents/Mask/Mask.module.css'
import dropdownStyles from '../../SharedComponents/Dropdown/Dropdown.module.css'
import fileStyles from '../../SharedComponents/File/File.module.css'
import signatureStyles from '../../SharedComponents/Signature/Signature.module.css'
import headerAndImageStyles from '../../SharedComponents/HeaderAndImage/HeaderAndImage.module.css'
import createStyles from './CreateComponents.module.css'
import paymentStyles from '../../SharedComponents/Payments/Payments.module.css'
import radioButtonStyles from '../../../../components/UI/RadioButtons/RadioButtons.module.css'
import JsTable from '../../SharedComponents/JsTable/JsTable'
import Accordion from '../../SharedComponents/Accordion/Accordion'
import { AdvancedFileUpload } from '../../SharedComponents/File/AdvancedFileUpload/AdvancedFileUpload'
import Image from 'next/image'
import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import { useDashboard } from '../../../../hooks/useDashboard'
import useUtilityFunctions from '../../../../hooks/useUtilityFunctions'

function RenderFormFields({
  metadata,
  wholeMetadata,
  assignValuesNested,
  openDialog,
  inputs,
  setMetadata,
  handleInputChange,
  nestedAssignMetadata,
  handleUpdateMetadata,
  onRowUpdate,
  files,
  setFiles,
  setInputs,
  authorName,
  handleUpdate,
  pageNumber,
  totalPages,
  dialogData,
  showDialog,
  setDialogData,
  toast,
  currentWorkflow,
  termsAndConds,
  setTermsAndConds,
  currentTaC,
  setIsSortableMoving,
  isSortableMoving,
  accordionId,
}) {
  const componentMapper = {
    text: CreateText,
    calendar: CreateCalendar,
    number: CreateNumber,
    textarea: CreateTextarea,
    mask: CreateMask,
    dropdown: CreateDropdown,
    time: CreateTime,
    multiselect: CreateMultiSelect,
    header: CreateHeader,
    image: CreateImage,
    file: CreateFileInput,
    richText: CreateRichTextInput,
    subtitle: CreateReadonlySubtitle,
    signature: CreateSignature,
    radiobutton: CreateMultiRadioButtons,
    checkbox: CreateCheckbox,
    address: CreateAddress,
    vendorDetails: CreateVendorDetails,
    requestors: CreateRequestors,
    pageBreak: CreatePageBreak,
    calculatedField: CreateCalculatedField,
    termsAndConditions: CreateTermsAndConditions,
    advancedFileUpload: CreateAdvancedFileUpload,
    versionedFileUpload: CreateVersionedFileUpload,
    securedField: CreateSecuredField,
    scale: CreateScaleRating,
    stars: CreateStarRating,
    autoComplete: CreateAutoComplete,
    tableComponent: CreateTableComponent,
    jsTable: CreateJsTableComponent,
    payments: CreatePayments,
    heading: CreateHeading,
    invisibleComponent: CreateInvisibleComponent,
    employeeLookup: CreateEmployeeLookup,
    accordion: CreateAccordion,
    userSuggestion: CreateUserSuggestion,
    objectLink: CreateObjectLink,
    objectLinkGrid: CreateObjectLinkGrid,
    grid: CreateGrid,
  }

  return (
    <>
      {Object.keys(metadata ?? {}).length === 0 && (
        <h5
          style={{ margin: '0 auto', padding: '1rem' }}
        >{`Drop field here`}</h5>
      )}

      {Object.keys(metadata ?? {}).length > 0 &&
        Object.keys(metadata).map((guid) => {
          const { type, name, divClassName, parentId } = metadata[guid]

          // This is here because we get invalid properties in metadata when we add components to any form canvas besides the first one.
          if (!type || componentMapper[type] === undefined) {
            console.error(`Invalid component type: ${type} for guid: ${guid}`)
            delete metadata[guid]
            return
          }

          return (
            <div className={clsx(divClassName)} key={guid}>
              <Sortable
                key={guid}
                id={guid}
                handleUpdate={handleUpdate}
                metadata={metadata[guid]}
                dialogData={dialogData}
                showDialog={showDialog}
                setDialogData={setDialogData}
                isSortableMoving={isSortableMoving}
                setIsSortableMoving={setIsSortableMoving}
                toast={toast}
                currentWorkflow={currentWorkflow}
                wholeMetadata={wholeMetadata}
              >
                {createElement(componentMapper[type], {
                  guid: guid,
                  setMetadata: setMetadata,
                  metadata: metadata[guid] ?? {},
                  wholeMetadata: wholeMetadata,
                  openDialog: openDialog,
                  inputs: inputs,
                  value: inputs[name],
                  onChange: handleInputChange,
                  nestedAssignMetadata,
                  handleUpdateMetadata,
                  handleUpdate,
                  onRowUpdate: onRowUpdate,
                  assignValuesNested: assignValuesNested,
                  showDialog,
                  setDialogData,
                  files: files,
                  setFiles: setFiles,
                  setInputs: setInputs,
                  authorName: authorName,
                  pageNumber: pageNumber,
                  totalPages: totalPages,
                  termsAndConds: termsAndConds,
                  setTermsAndConds: setTermsAndConds,
                  currentTaC: currentTaC,
                  accordionId: accordionId,
                })}
              </Sortable>
            </div>
          )
        })}
    </>
  )
}

export default function CreateComponents({
  metadata,
  assignValuesNested,
  openDialog,
  inputs,
  setMetadata,
  handleInputChange,
  nestedAssignMetadata,
  handleUpdateMetadata,
  onRowUpdate,
  files,
  setFiles,
  setInputs,
  authorName,
  handleUpdate,
  pageNumber,
  totalPages,
  dialogData,
  showDialog,
  setDialogData,
  toast,
  currentWorkflow,
  termsAndConds,
  setTermsAndConds,
  currentTaC,
  accordionId = null,
}) {
  const [isSortableMoving, setIsSortableMoving] = useState(false)

  const fields = Object.keys(metadata).reduce(
    (prev, curr) => {
      if (metadata[curr].parentId) {
        prev.childFields = {
          ...prev.childFields,
          [`${metadata[curr].parentId}$${metadata[curr].accordionIndex}`]: {
            ...prev.childFields[
              `${metadata[curr].parentId}$${metadata[curr].accordionIndex}`
            ],
            [curr]: metadata[curr],
          },
        }
      }
      if (!metadata[curr].parentId) {
        prev.parentFields = { ...prev.parentFields, [curr]: metadata[curr] }
      }
      return prev
    },
    {
      parentFields: {},
      childFields: {},
    }
  )

  return (
    <RenderFormFields
      metadata={
        accordionId
          ? fields.childFields[accordionId]
          : fields.parentFields ?? {}
      }
      wholeMetadata={metadata}
      assignValuesNested={assignValuesNested}
      openDialog={openDialog}
      inputs={inputs}
      setMetadata={setMetadata}
      handleInputChange={handleInputChange}
      nestedAssignMetadata={nestedAssignMetadata}
      handleUpdateMetadata={handleUpdateMetadata}
      onRowUpdate={onRowUpdate}
      files={files}
      setFiles={setFiles}
      setInputs={setInputs}
      authorName={authorName}
      handleUpdate={handleUpdate}
      pageNumber={pageNumber}
      totalPages={totalPages}
      dialogData={dialogData}
      showDialog={showDialog}
      setDialogData={setDialogData}
      toast={toast}
      currentWorkflow={currentWorkflow}
      termsAndConds={termsAndConds}
      setTermsAndConds={setTermsAndConds}
      currentTaC={currentTaC}
      setIsSortableMoving={setIsSortableMoving}
      isSortableMoving={isSortableMoving}
      accordionId={accordionId}
    />
  )
}

export function CreateInvisibleComponent({ metadata }) {
  return <div className="col-5 mlr-05" style={{ height: '50px' }}></div>
}

export function CreateText({
  metadata,
  value,
  onChange,
  openDialog,
  setMetadata,
}) {
  const {
    name,
    label,
    subtitle,
    defaultValue,
    alignment,
    validations,
    divClassName,
  } = metadata
  return (
    <ComponentContainer
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <SettingsButton openDialog={openDialog} componentData={metadata} />
      <AlignmentContainer alignment={alignment}>
        <LabelContainer alignment={alignment}>
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer divClassName={divClassName}>
          <InputText
            name={name}
            className={clsx('col-12', styles.input)}
            autoComplete="off"
            value={value ?? defaultValue}
            onChange={onChange}
            onFocus={(e) => {
              e.stopPropagation()
              openDialog(metadata)
            }}
            disabled
          />
          <Subtitle subtitle={subtitle} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function CreateCalendar({
  metadata,
  value,
  onChange,
  openDialog,
  setMetadata,
}) {
  const {
    name,
    label,
    subtitle,
    defaultValue,
    alignment,
    validations,
    isDefaultValueDynamic,
    divClassName,
  } = metadata
  const convertDataFormat = defaultValue ? new Date(defaultValue) : null

  const getValue = () => {
    if (isDefaultValueDynamic) {
      return new Date()
    } else {
      return value ?? convertDataFormat
    }
  }

  return (
    <ComponentContainer
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <SettingsButton openDialog={openDialog} componentData={metadata} />
      <AlignmentContainer alignment={alignment}>
        <LabelContainer alignment={alignment}>
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer divClassName={divClassName}>
          <Calendar
            style={{ width: '100%' }}
            name={name}
            className={clsx('col-12', calendarStyles.calendar)}
            value={getValue()}
            onChange={onChange}
            disabled
            onFocus={(e) => {
              e.stopPropagation()
              openDialog(metadata)
            }}
            dateFormat="mm/dd/yy"
          />
          <Subtitle subtitle={subtitle} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function CreateNumber({
  metadata,
  value,
  onChange,
  openDialog,
  setMetadata,
}) {
  const {
    name,
    validations,
    label,
    subtitle,
    alignment,
    defaultValue,
    divClassName,
  } = metadata

  return (
    <ComponentContainer
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <SettingsButton openDialog={openDialog} componentData={metadata} />
      <AlignmentContainer alignment={alignment}>
        <LabelContainer alignment={alignment}>
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer divClassName={divClassName}>
          <InputNumber
            style={{ width: '100%' }}
            name={name}
            id="numberInput"
            // style={{ width: "fill-available" }}
            className="formbuilder_numInput"
            value={value ?? defaultValue}
            onChange={onChange}
            useGrouping={false}
            disabled
            onFocus={(e) => {
              e.stopPropagation()
              openDialog(metadata)
            }}
          />
          <Subtitle subtitle={subtitle} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function CreateTextarea({
  metadata,
  value,
  onChange,
  openDialog,
  setMetadata,
}) {
  const {
    name,
    validations,
    label,
    subtitle,
    alignment,
    defaultValue,
    divClassName,
  } = metadata

  return (
    <ComponentContainer
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <SettingsButton openDialog={openDialog} componentData={metadata} />
      <AlignmentContainer alignment={alignment}>
        <LabelContainer
          alignment={alignment}
          className={clsx(
            `${textareaStyles.textareaLabel} ${createStyles.textareaLabelWidthContainer}`,
            alignment === 'column' && textareaStyles.topAligned,
            alignment === 'row-reverse' && textareaStyles.rightAligned
          )}
        >
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer
          className={createStyles.textareaInputsContainer}
          divClassName={divClassName}
        >
          <InputTextarea
            className={clsx('col-12', textareaStyles.textareaInput)}
            name={name}
            autoResize
            value={value ?? defaultValue}
            onChange={onChange}
            onFocus={(e) => {
              e.stopPropagation()
              openDialog(metadata)
            }}
            disabled
          />
          <Subtitle subtitle={subtitle} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function CreateMask({
  metadata,
  value,
  onChange,
  openDialog,
  setMetadata,
}) {
  const { name, label, subtitle, mask, alignment, validations, divClassName } =
    metadata

  return (
    <ComponentContainer
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <SettingsButton openDialog={openDialog} componentData={metadata} />
      <AlignmentContainer alignment={alignment}>
        <LabelContainer alignment={alignment}>
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer divClassName={divClassName}>
          <InputMask
            name={name}
            className={clsx('col-12', maskStyles.mask)}
            value={value}
            onChange={onChange}
            mask={mask}
            autoClear={false}
            disabled
            onFocus={(e) => {
              e.stopPropagation()
              openDialog(metadata)
            }}
          />
          <Subtitle subtitle={subtitle} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function CreateDropdown({ metadata, openDialog, value, onChange }) {
  const {
    name,
    validations,
    label,
    subtitle,
    options,
    defaultValue,
    alignment,
    divClassName,
  } = metadata

  return (
    <ComponentContainer
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <SettingsButton openDialog={openDialog} componentData={metadata} />
      <AlignmentContainer alignment={alignment}>
        <LabelContainer alignment={alignment}>
          {/* style={{
            minWidth: divClassName.includes("col-11") ? "24%" : "auto",
            maxWidth: divClassName.includes("col-11") ? "100%" : "auto", width: divClassName.includes("col-11") ? "auto" : "100%"
          }} */}
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer divClassName={divClassName}>
          <Dropdown
            name={name}
            className={clsx('col-12', dropdownStyles.dropdown)}
            value={value ?? defaultValue}
            onChange={onChange}
            options={options}
            disabled
            onFocus={(e) => {
              e.stopPropagation()
              openDialog(metadata)
            }}
          />
          <Subtitle subtitle={subtitle} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function CreateTime({
  metadata,
  value,
  onChange,
  openDialog,
  setMetadata,
}) {
  const {
    name,
    label,
    subtitle,
    defaultValue,
    alignment,
    validations,
    divClassName,
  } = metadata
  const convertDataFormat = defaultValue ? new Date(defaultValue) : null

  return (
    <ComponentContainer
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <SettingsButton openDialog={openDialog} componentData={metadata} />
      <AlignmentContainer alignment={alignment}>
        <LabelContainer alignment={alignment}>
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer divClassName={divClassName}>
          <Calendar
            style={{ width: '100%' }}
            name={name}
            className={clsx('col-12', calendarStyles.calendar)}
            timeOnly
            showTime
            disabled
            hourFormat="12"
            value={value ?? convertDataFormat}
            onChange={onChange}
            onFocus={(e) => {
              e.stopPropagation()
              openDialog(metadata)
            }}
          />
          <Subtitle subtitle={subtitle} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function CreateMultiSelect({
  metadata,
  openDialog,
  value,
  onChange,
  setMetadata,
}) {
  const {
    name,
    validations,
    label,
    subtitle,
    options,
    defaultValue,
    alignment,
    divClassName,
  } = metadata

  return (
    <ComponentContainer
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <SettingsButton openDialog={openDialog} componentData={metadata} />
      <AlignmentContainer alignment={alignment}>
        <LabelContainer alignment={alignment}>
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer divClassName={divClassName}>
          <MultiSelect
            name={name}
            className={`col-12 ${dropdownStyles.dropdown}`}
            value={value ?? defaultValue}
            onChange={onChange}
            options={options}
            display="chip"
            disabled
            onFocus={(e) => {
              e.stopPropagation()
              openDialog(metadata)
            }}
          />
          <Subtitle subtitle={subtitle} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function CreateHeader({
  metadata,
  openDialog,
  assignValuesNested,
  guid,
  value,
  files,
  setFiles,
  setInputs,
  setMetadata,
}) {
  const { name, label, width, height, aspectRatio, file, alignment } = metadata
  const userProfile = useContext(UserProfileContext)

  const isDesigner =
    userProfile?.role?.name == 'Designer' || userProfile?.role?.name == 'Admin'

  useEffect(() => {
    const fetchImage = async () => {
      try {
        const apiUrl = `${process.env.NEXT_PUBLIC_FORM_BUILDER_API}FormMetadata/file/${file.guid}`
        const response = await fetch(apiUrl)

        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`)
        }

        const blob = await response.blob()
        const reader = new FileReader()

        reader.onloadend = () => {
          setInputs((prevInputs) => ({ ...prevInputs, [name]: reader.result }))
        }

        reader.readAsDataURL(blob)
      } catch (error) {
        console.error('Error fetching image:', error)
      }
    }

    if (file) {
      fetchImage()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const updateMetadata = (width, height, aspectRatio = aspectRatio) => {
    setMetadata((prevMetadata) => {
      const currentComponentData = prevMetadata[guid] || {}
      const updatedComponentData = {
        ...currentComponentData,
        width: width,
        height: height,
        aspectRatio: aspectRatio,
      }

      return {
        ...prevMetadata,
        [guid]: updatedComponentData,
      }
    })
  }

  const handleDelete = () => {
    setFiles((prevFiles) => ({
      ...prevFiles,
      [guid]: undefined,
    }))

    setMetadata((prevMetadata) => {
      const updatedMetadata = { ...prevMetadata }
      if (updatedMetadata[guid]) {
        updatedMetadata[guid].file = undefined
      }
      return updatedMetadata
    })
    setInputs((prevInputs) => ({ ...prevInputs, [name]: undefined }))
  }

  const handleResize = (event, direction, ref, delta) => {
    updateMetadata(ref.style.width, ref.style.height, aspectRatio)
  }

  const handleImageUpload = (event) => {
    const file = event.target.files[0]

    setFiles((prevFiles) => ({
      ...prevFiles,
      [event.target.name]: file,
    }))

    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        assignValuesNested(name, e.target.result)

        const img = new Image()
        img.src = e.target.result
        img.onload = () => {
          const aspectRatio = width / height
          updateMetadata('100%', '100%', aspectRatio)
        }
      }
      reader.readAsDataURL(file)
    }
  }

  return (
    <div
      className="field grid grid-nogutter"
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <SettingsButton openDialog={openDialog} componentData={metadata} />
      <CustomFileInput
        name={guid}
        buttonText={
          file
            ? `File Name: ${file.fileName}`
            : files[guid]
            ? `File Name: ${files[guid].name}`
            : 'Choose a File'
        }
        accept="image/jpeg,image/png"
        multiple={false}
        onChange={handleImageUpload}
        onDelete={handleDelete}
        fileSelected={
          !!(metadata[guid] && metadata[guid].file) || !!files[guid]
        }
        disabled={!isDesigner}
      />
      <AlignmentContainer alignment={alignment} isHeader={true}>
        {value && (
          <div className="col-4">
            <Resizable
              size={{ width, height }}
              onResizeStop={handleResize}
              lockAspectRatio={true}
              maxWidth={'100%'}
              minWidth={40}
              enable={{
                right: true,
              }}
              defaultSize={{
                width: '100%',
                height: '100%',
              }}
            >
              <div className={headerAndImageStyles.imageWrapper}>
                <NextImage src={value} alt="Uploaded" fill />
              </div>
            </Resizable>
          </div>
        )}
        <div
          className="col-8"
          style={{ marginBottom: alignment === 'column' ? '0.5rem' : 0 }}
        >
          <ReadonlyLexicalEditor value={label} />
        </div>
      </AlignmentContainer>
      {/* <h1 className={clsx(value ? 'col-7' : 'col-12',  styles.label, 'mr-1')} >{label}</h1> */}
    </div>
  )
}

export function CreateImage({
  metadata,
  assignValuesNested,
  setMetadata,
  guid,
  value,
  openDialog,
  files,
  setFiles,
  setInputs,
}) {
  const {
    name,
    label,
    subtitle,
    width,
    height,
    aspectRatio,
    file,
    alignment,
    divClassName,
  } = metadata
  const userProfile = useContext(UserProfileContext)

  const isDesigner =
    userProfile?.role?.name == 'Designer' || userProfile?.role?.name == 'Admin'

  const handleDelete = () => {
    setFiles((prevFiles) => ({
      ...prevFiles,
      [guid]: undefined,
    }))
    setMetadata((prevMetadata) => {
      const updatedMetadata = { ...prevMetadata }
      if (updatedMetadata[guid]) {
        // Reset or remove the file property
        updatedMetadata[guid].file = undefined
      }
      return updatedMetadata
    })
    setInputs((prevInputs) => ({ ...prevInputs, [name]: undefined }))
  }

  useEffect(() => {
    const fetchImage = async () => {
      try {
        const apiUrl = `${process.env.NEXT_PUBLIC_FORM_BUILDER_API}FormMetadata/file/${file.guid}`
        const response = await fetch(apiUrl)

        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`)
        }

        const blob = await response.blob()
        const reader = new FileReader()

        reader.onloadend = () => {
          setInputs((prevInputs) => ({ ...prevInputs, [name]: reader.result }))
        }

        reader.readAsDataURL(blob)
      } catch (error) {
        console.error('Error fetching image:', error)
      }
    }

    if (file) {
      fetchImage()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const updateMetadata = (width, height, aspectRatio = aspectRatio) => {
    setMetadata((prevMetadata) => {
      const currentComponentData = prevMetadata[guid] || {}
      const updatedComponentData = {
        ...currentComponentData,
        width: width,
        height: height,
        aspectRatio: aspectRatio,
      }

      return {
        ...prevMetadata,
        [guid]: updatedComponentData,
      }
    })
  }

  const handleResize = (event, direction, ref, delta) => {
    updateMetadata(ref.style.width, ref.style.height, aspectRatio)
  }

  const handleImageUpload = (event) => {
    const file = event.target.files[0]

    setFiles((prevFiles) => ({
      ...prevFiles,
      [event.target.name]: file,
    }))

    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        assignValuesNested(name, e.target.result)

        const img = new Image()
        img.src = e.target.result
        img.onload = () => {
          const aspectRatio = width / height
          updateMetadata('100%', '100%', aspectRatio)
        }
      }
      reader.readAsDataURL(file)
    }
  }

  return (
    <ComponentContainer
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <SettingsButton openDialog={openDialog} componentData={metadata} />
      <AlignmentContainer
        alignment={alignment}
        style={{ flexDirection: 'column', display: 'flex' }}
      >
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            marginBottom: alignment === 'column' ? '0.5rem' : 0,
            marginLeft: alignment === 'row-reverse' ? '0.5rem' : 0,
          }}
        >
          <Label label={label} />
          <CustomFileInput
            name={guid}
            buttonText={
              metadata[guid] && metadata[guid].file
                ? `File Name: ${metadata[guid].file.fileName}`
                : files[guid]
                ? `File Name: ${files[guid].name}`
                : 'Choose a File'
            }
            accept="image/jpeg,image/png"
            multiple={false}
            onChange={handleImageUpload}
            onDelete={handleDelete}
            fileSelected={
              !!(metadata[guid] && metadata[guid].file) || !!files[guid]
            }
            disabled={!isDesigner}
            style={{ padding: '0.5rem 0' }}
          />
        </div>
        <InputsContainer
          className={headerAndImageStyles.inputsContainer}
          divClassName={divClassName}
        >
          {value && (
            <Resizable
              size={{ width, height }}
              onResizeStop={handleResize}
              lockAspectRatio={true}
              maxWidth={'100%'}
              minWidth={40}
              enable={{
                right: true,
              }}
              defaultSize={{
                width: '100%',
                height: '100%',
              }}
            >
              {
                // eslint-disable-next-line @next/next/no-img-element
                <div
                  className={clsx(
                    headerAndImageStyles.imageWrapper,
                    headerAndImageStyles.rightBorder
                  )}
                >
                  <NextImage src={value} alt={guid} fill />
                  <Subtitle subtitle={subtitle} />
                </div>
              }
            </Resizable>
          )}
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function CreateFileInput({
  metadata,
  openDialog,
  onChange,
  setMetadata,
  divClassName,
}) {
  const { name, label, multiple, subtitle, alignment, validations } = metadata

  return (
    <ComponentContainer
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <SettingsButton openDialog={openDialog} componentData={metadata} />
      <AlignmentContainer alignment={alignment}>
        <LabelContainer
          alignment={alignment}
          className={clsx(
            fileStyles.labelContainer,
            alignment === 'column' && fileStyles.topAligned,
            alignment === 'row-reverse' && fileStyles.rightAligned
          )}
        >
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer divClassName={divClassName}>
          <UrlFileInput
            name={name}
            className={clsx('col-12', fileStyles.file)}
            multiple={multiple}
            buttonText="Choose a File"
            onChange={onChange}
            disabled={true}
            onFocus={(e) => {
              e.stopPropagation()
              openDialog(metadata)
            }}
          />
          <Subtitle subtitle={subtitle} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function CreateRichTextInput({
  metadata,
  openDialog,
  value,
  onChange,
  setMetadata,
  divClassName,
}) {
  const { name, label, subtitle, alignment, validations } = metadata

  return (
    <ComponentContainer
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <SettingsButton openDialog={openDialog} componentData={metadata} />
      <AlignmentContainer alignment={alignment}>
        <LabelContainer
          // Before deleting the commented className check to make sure the
          // textareaLabel class have the same properties in both createStyles & textareaStyles
          // className={`${createStyles.textareaLabelWidthContainer} ${createStyles.textareaLabel}`}
          alignment={alignment}
          className={clsx(
            `${textareaStyles.textareaLabel} ${createStyles.textareaLabelWidthContainer}`,
            alignment === 'column' && textareaStyles.topAligned,
            alignment === 'row-reverse' && textareaStyles.rightAligned
          )}
        >
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer
          className={createStyles.textareaInputsContainer}
          divClassName={divClassName}
        >
          <LexicalEditor
            name={name}
            value={value}
            onChange={onChange}
            onFocus={(e) => {
              e.stopPropagation()
              openDialog(metadata)
            }}
            readOnly={true}
          />
          <Subtitle subtitle={subtitle} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function CreateReadonlySubtitle({ metadata, openDialog, setMetadata }) {
  const { subtitle } = metadata

  return (
    <div
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <SettingsButton componentData={metadata} openDialog={openDialog} />
      <ReadonlyLexicalEditor value={subtitle} />
    </div>
  )
}

export function CreateSignature({
  metadata,
  openDialog,
  value,
  onChange,
  setMetadata,
}) {
  const {
    name,
    label,
    subtitle,
    validations,
    fontStyle,
    alignment,
    divClassName,
  } = metadata

  return (
    <ComponentContainer onClick={() => openDialog(metadata)}>
      <SettingsButton componentData={metadata} openDialog={openDialog} />
      <AlignmentContainer alignment={alignment}>
        <LabelContainer alignment={alignment}>
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer divClassName={divClassName}>
          <InputText
            className={clsx('col-12', signatureStyles.signature)}
            name={name}
            value={value}
            onChange={onChange}
            style={{ fontFamily: fontStyle }}
            onFocus={() => openDialog(metadata)}
            disabled
          />
          <Subtitle subtitle={subtitle} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function CreateUserSuggestion({
  metadata,
  openDialog,
  value,
  onChange,
  setMetadata,
}) {
  const {
    name,
    label,
    subtitle,
    validations,
    fontStyle,
    alignment,
    divClassName,
  } = metadata

  return (
    <ComponentContainer
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <SettingsButton componentData={metadata} openDialog={openDialog} />
      <AlignmentContainer alignment={alignment}>
        <LabelContainer alignment={alignment}>
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer divClassName={divClassName}>
          <InputText
            className={clsx('col-12', signatureStyles.signature)}
            name={name}
            value={value}
            onChange={onChange}
            style={{ fontFamily: fontStyle }}
            onFocus={() => openDialog(metadata)}
            disabled
          />
          <Subtitle subtitle={subtitle} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function CreateMultiRadioButtons({
  metadata,
  openDialog,
  value,
  onChange,
}) {
  const {
    name,
    label,
    subtitle,
    options,
    otherOptionEnabled,
    otherOptions,
    disabled,
    validations,
    defaultValue,
    alignment,
    isMultiColumn,
  } = metadata

  return (
    <ComponentContainer
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <SettingsButton openDialog={openDialog} componentData={metadata} />
      <AlignmentContainer alignment={alignment}>
        <LabelContainer
          alignment={alignment}
          isCheckbox={true}
          className={createStyles.radioButtonLabelContainer}
          style={{
            textAlign: alignment === 'row-reverse' ? 'right' : '',
            marginLeft:
              alignment === 'column' || alignment === 'row' ? '0.5rem' : '',
            marginBottom: alignment === 'column' ? '0.5rem' : '',
          }}
        >
          <Label
            // style={{ marginRight: "1rem" }}
            label={label}
            validations={validations}
          />
        </LabelContainer>
        <div
          className={clsx(
            createStyles.radioInputContainer,
            isMultiColumn && createStyles.multiColumns
          )}
          style={{
            justifyContent:
              alignment === 'row-reverse' && isMultiColumn && 'flex-end',
            width: alignment === 'row-reverse' && !isMultiColumn && '100%',
          }}
        >
          {options.length > 0 ? (
            <RadioButtons
              name={name}
              disabled={true}
              options={options}
              otherEnabled={otherOptionEnabled}
              onChange={onChange}
              value={value ?? defaultValue}
            />
          ) : (
            <p>{'Click dialog to add radiobuttons'}</p>
          )}
          <Subtitle subtitle={subtitle} />
        </div>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function CreateCheckbox({
  metadata,
  onChange,
  openDialog,
  divClassName,
}) {
  const {
    name,
    label,
    subtitle,
    validations,
    defaultValue,
    options,
    alignment,
    isMultiColumn,
    otherOptionEnabled,
  } = metadata
  const defaultValueIds = options
    .map((option, index) => {
      if (defaultValue?.includes(option.value)) {
        return index
      }
    })
    .filter((id) => id !== undefined)
  const [checkedValues, setCheckedValues] = useState(metadata?.options)
  const [checkedIds, setCheckedIds] = useState([])
  const [eventObject, setEventObject] = useState({
    target: { name: name, value: [] },
  })

  useEffect(() => {
    setCheckedIds(defaultValueIds)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defaultValue])

  const onCheckboxChange = (e) => {
    let selectedCheckbox = [...checkedValues]
    let selectedId = [...checkedIds]

    if (e.checked) {
      selectedCheckbox.push(e.value)
      selectedId.push(e.target.id)
    } else {
      selectedCheckbox.splice(selectedCheckbox.indexOf(e.value), 1)
      selectedId.splice(selectedId.indexOf(e.target.id), 1)
    }

    setCheckedValues(selectedCheckbox)
    setCheckedIds(selectedId)

    setEventObject((prevState) => {
      let tempState = JSON.parse(JSON.stringify(prevState))
      tempState.target.value = selectedCheckbox
      return tempState
    })

    return {
      ...eventObject,
      target: { ...eventObject.target, value: selectedCheckbox },
    }
  }

  return (
    <ComponentContainer
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <SettingsButton openDialog={openDialog} componentData={metadata} />
      <AlignmentContainer alignment={alignment}>
        <LabelContainer
          alignment={alignment}
          isCheckbox={true}
          style={{
            textAlign: alignment === 'row-reverse' ? 'right' : '',
            marginLeft:
              alignment === 'column' || alignment === 'row' ? '0.5rem' : '',
          }}
        >
          {/* style={{ alignItems: "flex-start", marginBottom: "0.5rem" }}> */}
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer
          divClassName={divClassName}
          style={{
            width: alignment === 'row-reverse' && !isMultiColumn && '100%',
            justifyContent:
              alignment === 'row-reverse' && isMultiColumn && 'flex-end',
          }}
          className={clsx(
            isMultiColumn && createStyles.multiColumns,
            createStyles.checkboxWidth
          )}
        >
          {metadata.options.length > 0 ? (
            <>
              {metadata.options.map((checkboxes, index) => {
                return (
                  <div
                    className={radioButtonStyles.radioButtonContainer}
                    key={index}
                  >
                    <Checkbox
                      className="mr-1"
                      key={index}
                      id={index}
                      value={checkboxes.value}
                      disabled
                      onChange={(e) => onChange(onCheckboxChange(e))}
                      checked={checkedIds.some((id) => id === index)}
                    />
                    <label className={radioButtonStyles.radioButtonLabel}>
                      {checkboxes.value}
                    </label>
                  </div>
                )
              })}
              {otherOptionEnabled && (
                <div>
                  <Checkbox className="mr-1" disabled />
                  <label>Other:</label>
                </div>
              )}
            </>
          ) : (
            <p>{'Click dialog to add checkboxes'}</p>
          )}
          <Subtitle subtitle={subtitle} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function CreateAddress({
  metadata,
  value,
  onChange,
  openDialog,
  setMetadata,
}) {
  const { name, label, subtitle, defaultValue, validations, isFullSize } =
    metadata
  const [addressValue, setAddressValue] = useState('')
  const [filteredSuggestions, setFilteredSuggestions] = useState([])
  const [selectedAddress, setSelectedAddress] = useState(null)

  function handleAddressInputChange(e) {
    setAddressValue(e.target.value)
    onChange({ target: { name: name, value: e.target.value } })
  }

  function handleSelectSuggestion(e) {
    setSelectedAddress(e.value.addressObj)
  }

  async function handleAddressFilter(event) {
    if (!event.query) return

    try {
      const results = await fetch('/form-builder-studio/api/smarty', {
        method: 'POST',
        body: JSON.stringify({ address: event.query }),
        headers: {
          'Content-Type': 'application/json',
        },
      }).then((response) => response.json())

      const filteredAddresses = results.result.map((suggestion) => {
        const { streetLine, city, state, zipcode } = suggestion
        return {
          fullAddress: `${streetLine} ${city}, ${state} ${zipcode}`,
          addressObj: suggestion,
        }
      })

      setFilteredSuggestions(filteredAddresses)
    } catch (error) {
      console.error(error)
      setFilteredSuggestions([])
    }
  }

  return (
    <div
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <ComponentContainer className="grid grid-nogutter">
        <div className="col-12">
          <ComponentContainer>
            <SettingsButton openDialog={openDialog} componentData={metadata} />
            <LabelContainer
              className={`${createStyles.addressLabel} ${createStyles.addressLabelContainer}`}
            >
              <Label label={label} validations={validations} />
            </LabelContainer>
          </ComponentContainer>
        </div>
        <div className={createStyles.rowSetContainer}>
          <div
            className={
              isFullSize ? createStyles.columnTwelve : createStyles.columnSix
            }
          >
            <ComponentContainer>
              {!isFullSize && (
                <LabelContainer>
                  <Label label="Street" validations={validations} />
                </LabelContainer>
              )}
              <InputsContainer>
                <AutoComplete
                  className={createStyles.autoCompleteContainer}
                  inputClassName={createStyles.autoComplete}
                  field="fullAddress"
                  value={addressValue}
                  suggestions={filteredSuggestions}
                  completeMethod={handleAddressFilter}
                  onChange={(e) => handleAddressInputChange(e)}
                  onSelect={(e) => handleSelectSuggestion(e)}
                  onFocus={() => openDialog(metadata)}
                  disabled
                />
              </InputsContainer>
            </ComponentContainer>
          </div>
          {!isFullSize && (
            <div className={createStyles.columnSix}>
              <ComponentContainer>
                <LabelContainer>
                  <Label label="City" validations={validations} />
                </LabelContainer>
                <InputsContainer>
                  <InputText
                    className={styles.formbuilder_Input}
                    style={{ width: '100%' }}
                    value={selectedAddress?.city}
                    readOnly
                    disabled
                  />
                </InputsContainer>
              </ComponentContainer>
            </div>
          )}
        </div>
        {!isFullSize && (
          <div className={createStyles.rowSetContainer}>
            <div className={createStyles.columnSix}>
              <ComponentContainer>
                <LabelContainer>
                  <Label label="State" validations={validations} />
                </LabelContainer>
                <InputsContainer>
                  <InputText
                    className={styles.formbuilder_Input}
                    style={{ width: '100%' }}
                    value={selectedAddress?.state}
                    readOnly
                    disabled
                  />
                </InputsContainer>
              </ComponentContainer>
            </div>
            <div className={createStyles.columnSix}>
              <ComponentContainer>
                <LabelContainer>
                  <Label label="Zip" validations={validations} />
                </LabelContainer>
                <InputsContainer>
                  <InputText
                    style={{ width: '100%' }}
                    className={styles.formbuilder_Input}
                    value={selectedAddress?.zipcode}
                    readOnly
                    disabled
                  />
                </InputsContainer>
              </ComponentContainer>
            </div>
          </div>
        )}
        <div className={createStyles.addressSubtitleContainer}>
          <Subtitle subtitle={subtitle} />
        </div>
      </ComponentContainer>
    </div>
  )
}

export function CreateVendorDetails({
  metadata,
  value,
  onChange,
  openDialog,
  setMetadata,
}) {
  const { name, label, subtitle, defaultValue, validations } = metadata
  const [addressValue, setAddressValue] = useState('')
  const [filteredSuggestions, setFilteredSuggestions] = useState([])
  const [selectedAddress, setSelectedAddress] = useState(null)

  function handleAddressInputChange(e) {
    setAddressValue(e.target.value)
    onChange({ target: { name: name, value: e.target.value } })
  }

  function handleSelectSuggestion(e) {
    setSelectedAddress(e.value.addressObj)
  }

  async function handleAddressFilter(event) {
    if (!event.query) return

    try {
      const results = await fetch('/form-builder-studio/api/smarty', {
        method: 'POST',
        body: JSON.stringify({ address: event.query }),
        headers: {
          'Content-Type': 'application/json',
        },
      }).then((response) => response.json())

      const filteredAddresses = results.result.map((suggestion) => {
        const { streetLine, city, state, zipcode } = suggestion
        return {
          fullAddress: `${streetLine} ${city}, ${state} ${zipcode}`,
          addressObj: suggestion,
        }
      })

      setFilteredSuggestions(filteredAddresses)
    } catch (error) {
      console.error(error)
      setFilteredSuggestions([])
    }
  }

  return (
    <div
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <ComponentContainer className="grid grid-nogutter">
        <div className="col-12 ">
          <ComponentContainer>
            <SettingsButton openDialog={openDialog} componentData={metadata} />
            <LabelContainer
              className={`${createStyles.addressLabel} ${createStyles.addressLabelContainer}`}
            >
              <Label
                className={createStyles.titleStyle}
                label={'Supplier Details'}
                validations={validations}
              />
            </LabelContainer>
          </ComponentContainer>
        </div>
        <div className={createStyles.columnSetContainer}>
          <div className={createStyles.rowSetContainer}>
            <div className={createStyles.columnSix}>
              <ComponentContainer>
                <LabelContainer>
                  <Label label="Supplier Name" validations={validations} />
                </LabelContainer>
                <InputsContainer>
                  <AutoComplete
                    className={createStyles.autoCompleteContainer}
                    inputClassName={createStyles.autoComplete}
                    field="fullAddress"
                    value={addressValue}
                    suggestions={filteredSuggestions}
                    completeMethod={handleAddressFilter}
                    onChange={(e) => handleAddressInputChange(e)}
                    onSelect={(e) => handleSelectSuggestion(e)}
                    onFocus={() => openDialog(metadata)}
                    disabled
                  />
                </InputsContainer>
              </ComponentContainer>
            </div>
            <div className={createStyles.columnSix}>
              <ComponentContainer>
                <LabelContainer>
                  <Label label="Supplier Code" validations={validations} />
                </LabelContainer>
                <InputsContainer>
                  <InputText
                    className={styles.formbuilder_Input}
                    style={{ width: '100%' }}
                    value={selectedAddress?.state}
                    readOnly
                    disabled
                  />
                </InputsContainer>
              </ComponentContainer>
            </div>
          </div>
          <div className={createStyles.rowSetContainer}>
            <div className={createStyles.columnSix}>
              <ComponentContainer>
                <LabelContainer>
                  <Label label="Supplier Email" validations={validations} />
                </LabelContainer>
                <InputsContainer>
                  <InputText
                    className={styles.formbuilder_Input}
                    style={{ width: '100%' }}
                    value={selectedAddress?.state}
                    readOnly
                    disabled
                  />
                </InputsContainer>
              </ComponentContainer>
            </div>
            <div className={createStyles.columnSix}>
              <ComponentContainer>
                <LabelContainer>
                  <Label label="Contact No" validations={validations} />
                </LabelContainer>
                <InputsContainer>
                  <InputText
                    className={styles.formbuilder_Input}
                    style={{ width: '100%' }}
                    value={selectedAddress?.state}
                    readOnly
                    disabled
                  />
                </InputsContainer>
              </ComponentContainer>
            </div>
          </div>
        </div>
        <div className="col-12 mt-5">
          <ComponentContainer>
            <SettingsButton openDialog={openDialog} componentData={metadata} />
            <LabelContainer
              className={`${createStyles.addressLabel} ${createStyles.addressLabelContainer}`}
            >
              <Label
                className={createStyles.titleStyle}
                label={'Supplier Address'}
                validations={validations}
              />
            </LabelContainer>
          </ComponentContainer>
        </div>
        <div className={createStyles.columnSetContainer}>
          <div className={createStyles.rowSetContainer}>
            <div className={createStyles.columnSix}>
              <ComponentContainer>
                <LabelContainer>
                  <Label label="Address" validations={validations} />
                </LabelContainer>
                <InputsContainer>
                  <InputText
                    style={{ width: '100%' }}
                    className={styles.formbuilder_Input}
                    value={selectedAddress?.zipcode}
                    readOnly
                    disabled
                  />
                </InputsContainer>
              </ComponentContainer>
            </div>
            <div className={createStyles.columnSix}>
              <ComponentContainer>
                <LabelContainer>
                  <Label label="State" validations={validations} />
                </LabelContainer>
                <InputsContainer>
                  <InputText
                    style={{ width: '100%' }}
                    className={styles.formbuilder_Input}
                    value={selectedAddress?.zipcode}
                    readOnly
                    disabled
                  />
                </InputsContainer>
              </ComponentContainer>
            </div>
          </div>
          <div className={createStyles.rowSetContainer}>
            <div className={createStyles.columnSix}>
              <ComponentContainer>
                <LabelContainer>
                  <Label label="City" validations={validations} />
                </LabelContainer>
                <InputsContainer>
                  <InputText
                    style={{ width: '100%' }}
                    className={styles.formbuilder_Input}
                    value={selectedAddress?.zipcode}
                    readOnly
                    disabled
                  />
                </InputsContainer>
              </ComponentContainer>
            </div>
            <div className={createStyles.columnSix}>
              <ComponentContainer>
                <LabelContainer>
                  <Label label="Zip" validations={validations} />
                </LabelContainer>
                <InputsContainer>
                  <InputText
                    style={{ width: '100%' }}
                    className={styles.formbuilder_Input}
                    value={selectedAddress?.zipcode}
                    readOnly
                    disabled
                  />
                </InputsContainer>
              </ComponentContainer>
            </div>
          </div>
        </div>
        <div className={createStyles.addressSubtitleContainer0}>
          <Subtitle subtitle={subtitle} />
        </div>
      </ComponentContainer>
    </div>
  )
}

export function CreateRequestors({
  metadata,
  value,
  onChange,
  openDialog,
  setMetadata,
}) {
  const { name, label, subtitle, defaultValue, validations, sublabel } =
    metadata
  const [addressValue, setAddressValue] = useState('')
  const [filteredSuggestions, setFilteredSuggestions] = useState([])
  const [selectedAddress, setSelectedAddress] = useState(null)

  function handleAddressInputChange(e) {
    setAddressValue(e.target.value)
    onChange({ target: { name: name, value: e.target.value } })
  }

  function handleSelectSuggestion(e) {
    setSelectedAddress(e.value.addressObj)
  }

  async function handleAddressFilter(event) {
    if (!event.query) return

    try {
      const results = await fetch('/form-builder-studio/api/smarty', {
        method: 'POST',
        body: JSON.stringify({ address: event.query }),
        headers: {
          'Content-Type': 'application/json',
        },
      }).then((response) => response.json())

      const filteredAddresses = results.result.map((suggestion) => {
        const { streetLine, city, state, zipcode } = suggestion
        return {
          fullAddress: `${streetLine} ${city}, ${state} ${zipcode}`,
          addressObj: suggestion,
        }
      })

      setFilteredSuggestions(filteredAddresses)
    } catch (error) {
      console.error(error)
      setFilteredSuggestions([])
    }
  }

  return (
    <div
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <ComponentContainer className="grid grid-nogutter">
        <div className="col-12 p-0">
          <ComponentContainer>
            <SettingsButton openDialog={openDialog} componentData={metadata} />
            <LabelContainer
              className={`${createStyles.addressLabel} ${createStyles.addressLabelContainer}`}
            >
              <Label
                className={createStyles.titleStyle}
                label={label}
                validations={validations}
              />
            </LabelContainer>
          </ComponentContainer>
        </div>
        <div className={createStyles.rowSetContainer}>
          <div className={createStyles.columnSix}>
            <ComponentContainer>
              <LabelContainer>
                <Label
                  label={sublabel ? sublabel[0] : label}
                  validations={validations}
                />
              </LabelContainer>
              <InputsContainer>
                {/* <Dropdown style={{ width: '100%' }} /> */}
                <InputText
                  className={styles.formbuilder_Input}
                  style={{ width: '100%' }}
                  value={selectedAddress?.state}
                  readOnly
                  disabled
                />
                {/* <AutoComplete
                className={createStyles.autoCompleteContainer}
                inputClassName={createStyles.autoComplete}
                field="fullAddress"
                value={addressValue}
                suggestions={filteredSuggestions}
                completeMethod={handleAddressFilter}
                onChange={(e) => handleAddressInputChange(e)}
                onSelect={(e) => handleSelectSuggestion(e)}
                onFocus={(e) => {e.stopPropagation();openDialog(metadata)}}
                disabled
              /> */}
              </InputsContainer>
            </ComponentContainer>
          </div>
          <div className={createStyles.columnSix}>
            <ComponentContainer>
              <LabelContainer>
                <Label
                  label={sublabel ? sublabel[1] : label}
                  validations={validations}
                />
              </LabelContainer>
              <InputsContainer>
                {/* <Dropdown style={{ width: '100%' }} /> */}
                <InputText
                  className={styles.formbuilder_Input}
                  style={{ width: '100%' }}
                  value={selectedAddress?.state}
                  readOnly
                  disabled
                />
              </InputsContainer>
            </ComponentContainer>
          </div>
        </div>
        <div className={createStyles.rowSetContainer}>
          <div className={createStyles.columnSix}>
            <ComponentContainer>
              <LabelContainer>
                <Label
                  label={sublabel ? sublabel[2] : label}
                  validations={validations}
                />
              </LabelContainer>
              <InputsContainer>
                {/* <Dropdown style={{ width: '100%' }} /> */}
                <InputText
                  className={styles.formbuilder_Input}
                  style={{ width: '100%' }}
                  value={selectedAddress?.state}
                  readOnly
                  disabled
                />
              </InputsContainer>
            </ComponentContainer>
          </div>
          <div className={createStyles.columnSix}></div>
        </div>
        <div className={createStyles.addressSubtitleContainer}>
          <Subtitle subtitle={subtitle} />
        </div>
      </ComponentContainer>
    </div>
  )
}

export function CreatePageBreak({ metadata, openDialog, totalPages }) {
  const { backButtonText, nextButtonText, pageNumber } = metadata
  const buttonAlignment =
    pageNumber === 0
      ? 'flex-end'
      : pageNumber == totalPages.length - 1
      ? 'flex-start'
      : 'space-between'

  return (
    <ComponentContainer
      style={{
        justifyContent: 'flex-end',
        padding: '1rem 0',
        display: 'flex',
        flexDirection: 'row',
        columnGap: '20px',
      }}
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      {pageNumber !== 0 && (
        <SecondaryButton
          text={backButtonText}
          icon="pi-chevron-left"
          iconSize="14px"
          iconPosition="left"
        />
      )}
      {pageNumber !== totalPages.length - 1 && (
        <SecondaryButton
          text={nextButtonText}
          icon="pi-angle-double-right"
          iconSize="18px"
          iconPosition="right"
        />
      )}
    </ComponentContainer>
  )
}

export const CreateCalculatedField = ({ metadata, openDialog }) => {
  const { name, label, subtitle, validations, alignment, divClassName } =
    metadata

  return (
    <ComponentContainer
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <SettingsButton openDialog={openDialog} componentData={metadata} />
      <AlignmentContainer alignment={alignment} isCalculatedField={true}>
        <LabelContainer alignment={alignment} isCalculatedField={true}>
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer divClassName={divClassName}>
          <InputNumber
            style={{ width: '100%' }}
            disabled
            name={name}
            className={clsx(styles.formbuilder_Input)}
          />
          <Subtitle subtitle={subtitle} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export const CreateTermsAndConditions = ({
  metadata,
  openDialog,
  wholeMetadata,
  nestedAssignMetadata,
  termsAndConds,
  setTermsAndConds,
  currentTaC,
}) => {
  const { labelText, validations, guid, divClassName } = metadata
  const labelContainerStyle = {
    display: 'flex',
    justifyContent: 'flex-start',
    width: '3%',
    marginTop: '1rem',
  }

  function processString(inputString) {
    const regex = /{([^}]+)}/g

    const parts = inputString.split(regex).map((part, index) => {
      if (index % 2 === 1) {
        return (
          <span key={index} className={createStyles.linkStyle}>
            {part}
          </span>
        )
      } else {
        return part
      }
    })

    return <label>{parts}</label>
  }

  return (
    <>
      <div
        onClick={(e) => {
          e.stopPropagation()
          openDialog(metadata)
        }}
        className={createStyles.termsAndConditionsFlex}
      >
        <LabelContainer style={labelContainerStyle}>
          <span className={createStyles.termsAndConditionsRequiredColor}>
            {validations?.required?.isRequired ? `* ` : null}
          </span>
          <CheckBox disabled={true} style={{ alignSelf: 'flex-start' }} />
        </LabelContainer>
        <InputsContainer
          className={createStyles.termsAndConditionsFlex}
          divClassName={divClassName}
        >
          <div className={createStyles.termsAndConditionsTextAlignment}>
            {labelText
              ? processString(labelText)
              : processString('Click {here} to read our Terms and Conditions')}
          </div>
        </InputsContainer>
      </div>
    </>
  )
}

export const CreateAdvancedFileUpload = ({
  metadata,
  openDialog,
  assignValuesNested,
  onChange,
}) => {
  const {
    name,
    label,
    subtitle,
    validations,
    fileTypes,
    alignment,
    divClassName,
  } = metadata

  return (
    <ComponentContainer
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <AlignmentContainer alignment={alignment}>
        <LabelContainer
          className={
            alignment !== 'column' &&
            `${createStyles.textareaLabelWidthContainer} ${createStyles.textareaLabel}`
          }
          alignment={alignment}
        >
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer className={createStyles.textareaInputsContainer}>
          <AdvancedFileUpload
            value={[]}
            name={name}
            fileTypes={fileTypes}
            assignValuesNested={assignValuesNested}
            disabled
          />
          <div className="mt-5">
            <Subtitle subtitle={subtitle} />
          </div>
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export const CreateVersionedFileUpload = ({
  metadata,
  openDialog,
  assignValuesNested,
}) => {
  const {
    name,
    label,
    subtitle,
    validations,
    fileTypes,
    alignment,
    divClassName,
  } = metadata

  return (
    <ComponentContainer
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <AlignmentContainer alignment={alignment}>
        <LabelContainer
          className={
            alignment !== 'column' &&
            `${createStyles.textareaLabelWidthContainer} ${createStyles.textareaLabel}`
          }
          alignment={alignment}
        >
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer
          className={createStyles.textareaInputsContainer}
          divClassName={divClassName}
        >
          <FileInput
            value={[]}
            name={name}
            fileTypes={fileTypes}
            assignValuesNested={assignValuesNested}
            disabled
          />
          <div className="mt-5">
            <Subtitle subtitle={subtitle} />
          </div>
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export const CreateSecuredField = ({
  metadata,
  openDialog,
  value,
  onChange,
}) => {
  const { name, label, subtitle, validations, divClassName } = metadata

  return (
    <ComponentContainer
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <SettingsButton openDialog={openDialog} componentData={metadata} />
      <LabelContainer>
        <Label label={label} validations={validations} />
      </LabelContainer>
      <InputsContainer divClassName={divClassName}>
        <InputText
          name={name}
          className={clsx(styles.formbuilder_Input)}
          value={value}
          autoComplete="off"
          onFocus={(e) => {
            e.stopPropagation()
            openDialog(metadata)
          }}
          onChange={onChange}
          disabled
        />
        <Subtitle subtitle={subtitle} />
      </InputsContainer>
    </ComponentContainer>
  )
}

export function CreateAutoComplete({ metadata, openDialog, value, onChange }) {
  const {
    name,
    label,
    subtitle,
    validations,
    options,
    defaultValue,
    displayFields,
    displayFieldLabels,
    alignment,
    divClassName,
  } = metadata

  return (
    <ComponentContainer
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <SettingsButton openDialog={openDialog} componentData={metadata} />
      <div className={createStyles.autoCompleteContainer}>
        <div className={createStyles.autoCompleteFieldsContainer}>
          <AlignmentContainer alignment={alignment}>
            <LabelContainer alignment={alignment}>
              <Label label={label} validations={validations} />
            </LabelContainer>
            <InputsContainer divClassName={divClassName}>
              <AutoComplete
                name={name}
                style={{ width: '100%', paddingBottom: '0.5rem' }}
                inputClassName={createStyles.autoComplete}
                value={value}
                onFocus={(e) => {
                  e.stopPropagation()
                  openDialog(metadata)
                }}
                onChange={onChange}
                disabled
              />
            </InputsContainer>
          </AlignmentContainer>
        </div>
        {displayFields?.map((field, index) => {
          return (
            <div
              className={createStyles.autoCompleteFieldsContainer}
              key={index}
            >
              <AlignmentContainer alignment={alignment}>
                <LabelContainer alignment={alignment}>
                  <Label label={displayFieldLabels?.[field]}></Label>
                </LabelContainer>
                <InputsContainer divClassName={divClassName}>
                  <InputText
                    style={{
                      backgroundColor: '#E5E8EA',
                      height: '43px',
                      width: '100%',
                    }}
                    name={`${field}_${index}`}
                    disabled={true}
                    options={options}
                    onChange={onChange}
                    className={clsx(styles.formbuilder_Input)}
                    // value={}
                  />
                </InputsContainer>
              </AlignmentContainer>
            </div>
          )
        })}
      </div>
      <ComponentContainer>
        <Subtitle subtitle={subtitle} />
      </ComponentContainer>
    </ComponentContainer>
  )
}

export function CreateScaleRating({ metadata, value, onChange, openDialog }) {
  const {
    label,
    subtitle,
    validations,
    columns,
    rows,
    alignment,
    divClassName,
  } = metadata
  const componentContainerStyle = { flexWrap: 'nowrap' }
  const labelContainerStyle = { width: '12.9%' }
  const inputContainerStyle = { width: '83.5%' }

  return (
    <ComponentContainer
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
      style={componentContainerStyle}
    >
      <SettingsButton openDialog={openDialog} componentData={metadata} />
      <AlignmentContainer alignment={alignment}>
        <LabelContainer style={labelContainerStyle} alignment={alignment}>
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer
          divClassName={divClassName}
          style={{
            paddingLeft: alignment === 'column' ? '10px' : '0.5rem',
            width: alignment === 'column' ? '100%' : '50%',
          }}
        >
          <NumberScale columns={columns} rows={rows} disabled={true} />
          <Subtitle subtitle={subtitle} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function CreateStarRating({ metadata, value, onChange, openDialog }) {
  const { name, label, subtitle, validations, alignment, divClassName } =
    metadata

  return (
    <ComponentContainer
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <SettingsButton openDialog={openDialog} componentData={metadata} />
      <AlignmentContainer alignment={alignment}>
        <LabelContainer alignment={alignment}>
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer
          divClassName={divClassName}
          style={{
            paddingLeft: alignment === 'column' ? '10px' : '0.5rem',
            width: alignment === 'row-reverse' ? '50%' : '66.666%',
          }}
        >
          <Rating
            name={name}
            value={value}
            onChange={onChange}
            className={clsx(styles.formbuilder_Input, 'custom-rating')}
            onFocus={(e) => {
              e.stopPropagation()
              openDialog(metadata)
            }}
            cancel={false}
            disabled
          />
          <Subtitle subtitle={subtitle} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function CreateTableComponent({
  guid,
  metadata,
  headers,
  sourceTable,
  openDialog,
  value,
  onChange,
  assignValuesNested,
  nestedAssignMetadata,
  handleUpdateMetadata,
}) {
  const { name, label, width, height, aspectRatio, file, alignment } = metadata

  const onRowUpdate = (resultObj, value) => {
    nestedAssignMetadata(`${guid}.tableComponent`, [
      { resultObj: resultObj },
      { cellData: value },
    ])
    // assignValuesNested(`${guid}.tableComponent`, value)
  }

  return (
    <ComponentContainer
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
      style={{ overflowX: 'auto' }}
    >
      <div>
        {/* <SettingsButton openDialog={openDialog} componentData={metadata} /> */}
        <Label label={label} />
        {/* <HandsonTable /> */}
        <TableComponent
          name={name}
          onRowUpdate={onRowUpdate}
          metadata={metadata}
          value={value}
          assignValuesNested={assignValuesNested}
        />
      </div>
    </ComponentContainer>
  )
}

export function CreateJsTableComponent({
  guid,
  metadata,
  wholeMetadata,
  headers,
  sourceTable,
  openDialog,
  value,
  onChange,
  assignValuesNested,
  nestedAssignMetadata,
  handleUpdateMetadata,
  setMetadata,
}) {
  const { name, label, width, height, aspectRatio, file, alignment } = metadata

  const onRowUpdate = (resultObj, value) => {
    nestedAssignMetadata(`${guid}.tableComponent`, [
      { resultObj: resultObj },
      { cellData: value },
    ])
    // assignValuesNested(`${guid}.tableComponent`, value)
  }

  return (
    <ComponentContainer
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
      style={{ overflowX: 'auto' }}
    >
      <div>
        {/* <SettingsButton openDialog={openDialog} componentData={metadata} /> */}
        <Label label={label} />
        <JsTable
          name={name}
          guid={guid}
          metadata={metadata}
          wholeMetadata={wholeMetadata}
          setMetadata={setMetadata}
          handleInputChange={onChange}
          handleUpdateMetadata={handleUpdateMetadata}
        />
      </div>
    </ComponentContainer>
  )
}

export function CreatePayments({ metadata, value, openDialog, onChange }) {
  const { label, description, validations, price } = metadata
  const [isPaymentDetailsVisible, setIsPaymentDetailsVisible] = useState(false)
  const [isBillingAddressVisible, setIsBillingAddressVisible] = useState(false)
  const paymentDetailsStyle = {
    textAlign: 'right',
    width: '14.5%',
    display: 'flex',
    justifyContent: 'flex-end',
    alignItems: 'center',
  }
  const addressForFormBuilder = {
    // This is here just so that the address fields in the payment component are disabled.
    name: 'address',
    label: '',
    subtitle: defaultSubtitle,
    validations: { required: { isRequired: true } },
    disabled: true, // This is the only difference between this object and the address object found in the payment component in the Component Panel.
  }

  const handleCurrencyProperties = (propertyType, currencyLabel) => {
    if (!currencyLabel) return

    if (propertyType === 'inputId') {
      return `currency-${
        currencyLabel.includes('USD')
          ? 'us'
          : currencyLabel.includes('₹')
          ? 'india'
          : currencyLabel.includes('€')
          ? 'germany'
          : 'us'
      }`
    }

    if (propertyType === 'currency') {
      return `${
        currencyLabel.includes('USD')
          ? 'USD'
          : currencyLabel.includes('₹')
          ? 'INR'
          : currencyLabel.includes('€')
          ? 'EUR'
          : 'USD'
      }`
    }

    if (propertyType === 'locale') {
      return `${
        currencyLabel.includes('USD')
          ? 'en-US'
          : currencyLabel.includes('₹')
          ? 'en-IN'
          : currencyLabel.includes('€')
          ? 'de-DE'
          : 'en-US'
      }`
    }
  }

  const currencyProperties = {
    inputId: handleCurrencyProperties('inputId', metadata.currentCurrency),
    currency: handleCurrencyProperties('currency', metadata.currentCurrency),
    locale: handleCurrencyProperties('locale', metadata.currentCurrency),
  }

  return (
    <div
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <SettingsButton componentData={metadata} openDialog={openDialog} />

      <div>
        <h3>Product</h3>
        <div className={paymentStyles.itemSectionContainer}>
          <div className={paymentStyles.nameDescription}>
            <Label label={label} />
            <Label label={description} />
          </div>
          <InputNumber
            style={{ width: '28.5%' }}
            className={clsx(numberStyles.number)}
            disabled
            value={price}
            mode="currency"
            inputId={`currency-${currencyProperties.inputId}`}
            currency={`${currencyProperties.currency}`}
            locale={`${currencyProperties.locale}`}
          />
        </div>
      </div>

      <div>
        <div style={{ margin: '18.72px 0' }}>
          <h3 className={paymentStyles.dropdownContainer}>Payment Details</h3>
          <span
            className={paymentStyles.arrow}
            onClick={() => setIsPaymentDetailsVisible((prev) => !prev)}
          >
            <i
              className="pi pi-angle-down"
              style={{
                fontSize: '1.2rem',
                transform: isPaymentDetailsVisible ? 'rotate(180deg)' : '',
              }}
            />
          </span>
        </div>
        <div className={paymentStyles.paymentDetailsSectionContainer}>
          {isPaymentDetailsVisible && (
            <>
              <div className={paymentStyles.paymentInputField}>
                <Label
                  label={'Name on Card'}
                  validations={validations}
                  style={paymentDetailsStyle}
                />
                <InputText className={clsx(styles.input)} disabled />
              </div>
              <div className={paymentStyles.paymentInputField}>
                <Label
                  label={'Card Number'}
                  validations={validations}
                  style={paymentDetailsStyle}
                />
                <InputMask className={clsx(maskStyles.mask)} disabled={true} />
              </div>
              <div className={paymentStyles.paymentInputField}>
                <Label
                  label={'Card Expiration'}
                  validations={validations}
                  style={paymentDetailsStyle}
                />
                <InputMask className={clsx(maskStyles.mask)} disabled />
              </div>
              <div className={paymentStyles.paymentInputField}>
                <Label
                  label={'Security Code'}
                  validations={validations}
                  style={paymentDetailsStyle}
                />
                <InputNumber
                  className={clsx(numberStyles.number)}
                  disabled
                  style={{ width: '100%' }}
                />
              </div>
              <div className={paymentStyles.paymentInputField}>
                <Label
                  label={'Email Address'}
                  validations={validations}
                  style={paymentDetailsStyle}
                />
                <InputMask className={clsx(maskStyles.mask)} disabled />
              </div>
            </>
          )}
        </div>
      </div>

      <div>
        <div style={{ margin: '18.72px 0' }}>
          <h3 className={paymentStyles.dropdownContainer}>Billing Address</h3>
          <span
            className={paymentStyles.arrow}
            onClick={() => setIsBillingAddressVisible((prev) => !prev)}
          >
            <i
              className="pi pi-angle-down"
              style={{
                fontSize: '1.2rem',
                transform: isBillingAddressVisible ? 'rotate(180deg)' : '',
              }}
            />
          </span>
        </div>
        {isBillingAddressVisible && (
          <ViewAddress
            metadata={addressForFormBuilder}
            onChange={onChange}
            isForPayment={true}
            isForFormBuilder={true}
          />
        )}
      </div>
    </div>
  )
}

export function CreateHeading({ metadata, openDialog }) {
  const { subtitle, alignment } = metadata

  return (
    <div
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <AlignmentContainer alignment={alignment} isHeading={true}>
        <SettingsButton openDialog={openDialog} componentData={metadata} />
        <ReadonlyLexicalEditor value={subtitle} />
      </AlignmentContainer>
    </div>
  )
}

export function CreateEmployeeLookup({
  metadata,
  openDialog,
  value,
  onChange,
}) {
  const { name, label, subtitle, validations, alignment, divClassName } =
    metadata

  return (
    <ComponentContainer
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <SettingsButton openDialog={openDialog} componentData={metadata} />
      <div className={createStyles.autoCompleteContainer}>
        <div className={createStyles.autoCompleteFieldsContainer}>
          <AlignmentContainer alignment={alignment}>
            <LabelContainer alignment={alignment}>
              <Label label={label} validations={validations} />
            </LabelContainer>
            <InputsContainer divClassName={divClassName}>
              <AutoComplete
                name={name}
                style={{ width: '100%', paddingBottom: '0.5rem' }}
                inputClassName={createStyles.autoComplete}
                value={value}
                onFocus={(e) => {
                  e.stopPropagation()
                  openDialog(metadata)
                }}
                onChange={onChange}
                disabled
              />
            </InputsContainer>
          </AlignmentContainer>
        </div>
      </div>
      <ComponentContainer>
        <Subtitle subtitle={subtitle} />
      </ComponentContainer>
    </ComponentContainer>
  )
}

export function CreateAccordion({
  metadata,
  setMetadata,
  wholeMetadata,
  openDialog,
  inputs,
  onChange,
  // guid,
  assignValuesNested,
  nestedAssignMetadata,
  handleUpdateMetadata,
  handleUpdate,
  onRowUpdate,
  files,
  setFiles,
  setInputs,
  showDialog,
  setDialogData,
  authorName,
  pageNumber,
  totalPages,
  termsAndConds,
  setTermsAndConds,
  currentTaC,
}) {
  const { accordionTabs, guid } = metadata

  const activeIndices = accordionTabs
    .map((tab, index) => (tab.isExpanded ? index : -1)) // Map to index or -1
    .filter((index) => index !== -1) // Filter out the -1 values

  return (
    <ComponentContainer
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <Accordion
        sections={accordionTabs}
        guid={guid}
        metadata={wholeMetadata}
        setMetadata={setMetadata}
        openDialog={openDialog}
        inputs={inputs}
        handleInputChange={onChange}
        assignValuesNested={assignValuesNested}
        nestedAssignMetadata={nestedAssignMetadata}
        handleUpdateMetadata={handleUpdateMetadata}
        handleUpdate={handleUpdate}
        onRowUpdate={onRowUpdate}
        files={files}
        setFiles={setFiles}
        setInputs={setInputs}
        showDialog={showDialog}
        setDialogData={setDialogData}
        authorName={authorName}
        pageNumber={pageNumber}
        totalPages={totalPages}
        termsAndConds={termsAndConds}
        setTermsAndConds={setTermsAndConds}
        currentTaC={currentTaC}
      ></Accordion>
    </ComponentContainer>
  )
}

export function CreateObjectLink({ metadata, openDialog, value, onChange }) {
  const { label, subtitle, divClassName, alignment, validations } = metadata

  return (
    <ComponentContainer
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <SettingsButton openDialog={openDialog} componentData={metadata} />
      <AlignmentContainer alignment={alignment}>
        <LabelContainer alignment={alignment}>
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer divClassName={divClassName}>
          <AutoComplete
            value={value}
            suggestions={''}
            completeMethod={''}
            onChange={onChange}
            className={createStyles.autoCompleteContainer}
            inputClassName={createStyles.autoComplete}
            disabled
          />
          <Subtitle subtitle={subtitle} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function CreateObjectLinkGrid({ metadata, openDialog, inputs }) {
  const {
    label,
    subtitle,
    divClassName,
    alignment,
    validations,
    resultFields,
    primarySearchField,
    additionalColumns,
    rows,
  } = metadata
  const dataTableColumns = mapFields(
    resultFields,
    primarySearchField,
    additionalColumns
  )

  console.log('form link', resultFields, dataTableColumns, primarySearchField)

  function mapFields(
    resultFields,
    primarySearchField,
    additionalColumns,
    inputs = undefined
  ) {
    if (!resultFields || !primarySearchField) return []
    const mappedFields = []

    // Add primary search field first
    if (primarySearchField?.name && primarySearchField?.label) {
      mappedFields.push({
        field: primarySearchField.name,
        header: inputs
          ? inputs.primaryFieldDisplayName
          : (primarySearchField?.displayName || primarySearchField?.label) ??
            '',
      })
    }

    // Map each result field
    resultFields.forEach((field) => {
      if (field?.id && field?.name) {
        mappedFields.push({
          field: field.id,
          header: field.name,
        })
      }
    })

    // Map additional columns
    if (additionalColumns) {
      additionalColumns.forEach((column, index) => {
        if (column.title) {
          mappedFields.push({
            field: `${column.title}-${index}`,
            header: column.title,
            dataType: column.dataType,
            isRequired: column.isRequired,
          })
        }
      })
    }

    return mappedFields
  }

  const dateDisplayTemplate = (rowData, field) => {
    if (
      rowData[field] &&
      (Object.prototype.toString.call(rowData[field]) === '[object Date]' ||
        !isNaN(new Date(rowData[field])))
    ) {
      return createDashboardDate(rowData[field])
    } else {
      return ''
    }
  }

  const autoCompleteTemplate = () => {
    return (
      <>
        <AutoComplete disabled={true} />
      </>
    )
  }

  const textTemplate = () => {
    return <InputText disabled={true} />
  }

  const numberTemplate = () => {
    return <InputNumber disabled={true} />
  }

  const currencyTemplate = () => {
    return <InputNumber mode="currency" currency="USD" disabled={true} />
  }

  const selectableDateTemplate = () => {
    return <Calendar disabled={true} />
  }

  const todayDateTemplate = () => {
    return <Calendar disabled={true} />
  }

  const dropdownTemplate = () => {
    return <Dropdown disabled={true} />
  }

  const multiselectTemplate = () => {
    return <MultiSelect disabled={true} />
  }

  return (
    <ComponentContainer
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <SettingsButton openDialog={openDialog} componentData={metadata} />
      <AlignmentContainer alignment={alignment}>
        <LabelContainer alignment={alignment}>
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer
          divClassName={divClassName}
          style={{ maxWidth: '800px', minWidth: '800px' }}
        >
          {dataTableColumns.length > 0 ? (
            <DataTable value={rows}>
              {dataTableColumns.map((column, index) => (
                <Column
                  key={`${column.field}-${index}`}
                  field={column.field}
                  header={
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '4px',
                      }}
                    >
                      {column.isRequired && (
                        <span style={{ color: 'red' }}>*</span>
                      )}
                      {column.header}
                    </div>
                  }
                  body={
                    index === 0
                      ? autoCompleteTemplate
                      : column.field.includes('calendar') && !column?.dataType
                      ? (rowData) => dateDisplayTemplate(rowData, column.field)
                      : column?.dataType === 'text'
                      ? textTemplate
                      : column?.dataType === 'number'
                      ? numberTemplate
                      : column?.dataType === 'currency' ||
                        column?.dataType === 'percentage'
                      ? currencyTemplate
                      : column?.dataType === 'selectableDate'
                      ? selectableDateTemplate
                      : column?.dataType === 'todayDate'
                      ? todayDateTemplate
                      : column?.dataType === 'dropdown'
                      ? dropdownTemplate
                      : column?.dataType === 'multiselect'
                      ? multiselectTemplate
                      : null
                  }
                />
              ))}
            </DataTable>
          ) : (
            'Please Configure Component in Settings'
          )}
          <Subtitle subtitle={subtitle} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function CreateGrid({ metadata, openDialog, inputs }) {
  const {
    label,
    subtitle,
    divClassName,
    alignment,
    validations,
    resultFields,
    primarySearchField,
    additionalColumns,
    dataTableColumns,
    rows,
  } = metadata

  console.log(
    'grid',
    dataTableColumns,
    mapFields([], primarySearchField, dataTableColumns)
  )

  console.log('grid metadata', metadata, inputs)

  function mapFields(resultFields, primarySearchField, dataTableColumns) {
    // if (!resultFields || !primarySearchField) return [];
    const mappedFields = []

    // Add primary search field first
    if (primarySearchField?.name && primarySearchField?.label) {
      mappedFields.push({
        field: primarySearchField.name,
        header: metadata?.primaryFieldDisplayName
          ? metadata.primaryFieldDisplayName
          : (primarySearchField?.displayName || primarySearchField?.label) ??
            '',
      })
    }

    // Map each result field
    resultFields.forEach((field) => {
      if (field?.id && field?.name) {
        mappedFields.push({
          field: field.id,
          header: field.name,
        })
      }
    })
    console.log('onMap', mappedFields, dataTableColumns)
    // Map additional columns
    if (dataTableColumns) {
      dataTableColumns.forEach((column, index) => {
        if (column.title) {
          mappedFields.push({
            field: `${column.title}-${index}`,
            header: column.title,
            // dataType: column.dataType,
            // isRequired: column.isRequired,
          })
        }
      })
    }

    return mappedFields
  }

  const dateDisplayTemplate = (rowData, field) => {
    if (
      rowData[field] &&
      (Object.prototype.toString.call(rowData[field]) === '[object Date]' ||
        !isNaN(new Date(rowData[field])))
    ) {
      return createDashboardDate(rowData[field])
    } else {
      return 'test6'
    }
  }

  const autoCompleteTemplate = () => {
    return (
      <>
        <AutoComplete disabled={true} />
      </>
    )
  }

  const textTemplate = () => {
    return <InputText disabled={true} />
  }

  const numberTemplate = () => {
    return <InputNumber disabled={true} />
  }

  const currencyTemplate = () => {
    return <InputNumber mode="currency" currency="USD" disabled={true} />
  }

  const selectableDateTemplate = () => {
    return <Calendar disabled={true} />
  }

  const todayDateTemplate = () => {
    return <Calendar disabled={true} />
  }

  const dropdownTemplate = () => {
    return <Dropdown disabled={true} />
  }

  const multiselectTemplate = () => {
    return <MultiSelect disabled={true} />
  }
  const CovertDatatablevaluesToColumnData = (selectedColumns) => {
    return selectedColumns.map((column) => {
      return {
        title: (column.name || column.id) ?? '',
        dataType: 'text',
        isRequired: false,
      }
    })
  }
  console.log('grid', dataTableColumns)
  return (
    <ComponentContainer
      onClick={(e) => {
        e.stopPropagation()
        openDialog(metadata)
      }}
    >
      <SettingsButton openDialog={openDialog} componentData={metadata} />
      <AlignmentContainer alignment={alignment}>
        <LabelContainer alignment={alignment}>
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer
          divClassName={divClassName}
          style={{ maxWidth: '910px', minWidth: '800px' }}
        >
          {Array.isArray(dataTableColumns) ? (
            <DataTable value={rows}>
              {mapFields([], primarySearchField, dataTableColumns).map(
                (column, index) => (
                  <Column
                    key={`${column.field}-${index}`}
                    field={column.field}
                    header={
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '4px',
                        }}
                      >
                        {column.isRequired && (
                          <span style={{ color: 'red' }}>*</span>
                        )}
                        {column.header}
                      </div>
                    }
                    body={
                      index === 0
                        ? autoCompleteTemplate
                        : column.field.includes('calendar') && !column?.dataType
                        ? (rowData) =>
                            dateDisplayTemplate(rowData, column.field)
                        : column?.dataType === 'text'
                        ? textTemplate
                        : column?.dataType === 'number'
                        ? numberTemplate
                        : column?.dataType === 'currency' ||
                          column?.dataType === 'percentage'
                        ? currencyTemplate
                        : column?.dataType === 'selectableDate'
                        ? selectableDateTemplate
                        : column?.dataType === 'todayDate'
                        ? todayDateTemplate
                        : column?.dataType === 'dropdown'
                        ? dropdownTemplate
                        : column?.dataType === 'multiselect'
                        ? multiselectTemplate
                        : null
                    }
                  />
                )
              )}
            </DataTable>
          ) : (
            'Please Configure Component in Settings'
          )}
          <Subtitle subtitle={subtitle} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export const componentMapper = {
  text: CreateText,
  calendar: CreateCalendar,
  number: CreateNumber,
  textarea: CreateTextarea,
  mask: CreateMask,
  dropdown: CreateDropdown,
  time: CreateTime,
  multiselect: CreateMultiSelect,
  header: CreateHeader,
  image: CreateImage,
  file: CreateFileInput,
  richText: CreateRichTextInput,
  subtitle: CreateReadonlySubtitle,
  signature: CreateSignature,
  radiobutton: CreateMultiRadioButtons,
  checkbox: CreateCheckbox,
  address: CreateAddress,
  vendorDetails: CreateVendorDetails,
  requestors: CreateRequestors,
  pageBreak: CreatePageBreak,
  calculatedField: CreateCalculatedField, //
  termsAndConditions: CreateTermsAndConditions,
  advancedFileUpload: CreateAdvancedFileUpload,
  securedField: CreateSecuredField,
  scale: CreateScaleRating,
  stars: CreateStarRating,
  autoComplete: CreateAutoComplete,
  tableComponent: CreateTableComponent,
  payments: CreatePayments,
  heading: CreateHeading,
  invisibleComponent: CreateInvisibleComponent,
  employeeLookup: CreateEmployeeLookup,
  objectLink: CreateObjectLink,
  objectLinkGrid: CreateObjectLinkGrid,
  grid: CreateGrid,
}
