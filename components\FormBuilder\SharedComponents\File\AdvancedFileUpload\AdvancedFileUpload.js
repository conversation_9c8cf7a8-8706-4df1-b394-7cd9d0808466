import { useRef, useState } from 'react'
import { Dropdown } from 'primereact/dropdown'
import { Tooltip } from 'primereact/tooltip'
import { saveAs } from 'file-saver'
import { Button } from 'primereact/button'
import { InputText } from 'primereact/inputtext'
import { Toast } from 'primereact/toast'
import Backarrow from '../../../../../svg/backArrow_right.svg'
import Image from 'next/image'

import styles from './AdvancedFileUpload.module.css'

// Ahmet: This component is pretty complicated, message to me if you have any questions about
export const AdvancedFileUpload = ({
  name,
  disabled,
  fileTypes,
  files,
  value,
  onChange,
  isMobile = false,
}) => {
  const [dragActive, setDragActive] = useState(false)
  const inputRef = useRef(null)
  const toastRef = useRef(null)
  const urlInputRef = useRef(null)

  const handleButtonClick = () => {
    if (disabled) {
      return
    }

    inputRef.current.click()
  }

  const handleDrag = (event) => {
    event.preventDefault()
    event.stopPropagation()

    if (disabled) {
      return
    }

    if (event.type === 'dragenter' || event.type === 'dragover') {
      setDragActive(true)
    } else if (event.type === 'dragleave') {
      setDragActive(false)
    }
  }

  const updateFiles = (addedFiles) => {
    const currentFiles = Array.from(files || [])
    let newFiles

    if (addedFiles instanceof FileList) {
      newFiles = Array.from(addedFiles)
    } else {
      newFiles = Array.isArray(addedFiles) ? addedFiles : [addedFiles]
    }

    // Filters out files larger than 5MB - Yibran
    const validFiles = newFiles.filter((file) => {
      if (file.size > 5 * 1024 * 1024) {
        toastRef.current.show({
          severity: 'error',
          summary: 'File Too Large',
          detail: `File "${file.name}" exceeds 5MB limit`,
          life: 3000,
        })
        return false
      }
      return true
    })

    if (validFiles.length === 0) return

    const uniqueNewFiles = validFiles.filter(
      (newFile) =>
        !currentFiles.some((currentFile) => currentFile.name === newFile.name)
    )

    const combinedFiles = [...currentFiles, ...uniqueNewFiles]
    const updatedFileTypes = Array.isArray(value) ? [...value] : []

    uniqueNewFiles.forEach(() => {
      updatedFileTypes.push({ fileType: '' })
    })

    onChange({ target: { name: name, files: combinedFiles } })
    onChange({ target: { name: name, value: updatedFileTypes } })
  }

  const handleFileUpload = (event) => {
    event.preventDefault()
    event.stopPropagation()

    if (disabled) {
      return
    }

    if (event.target.files && event.target.files.length > 0) {
      updateFiles(event.target.files)
      event.target.value = null
    }
  }

  const handleUrlSubmit = () => {
    const url = urlInputRef.current.value

    if (url.trim() === '') {
      toastRef.current.show({
        severity: 'warn',
        summary: 'URL Required',
        detail: 'Please enter a URL.',
        life: 3000,
      })
      return
    }

    if (!url.startsWith('https://')) {
      toastRef.current.show({
        severity: 'error',
        summary: 'Invalid URL',
        detail: 'Only https URLs are allowed.',
        life: 3000,
      })
      return
    }

    const fileType = determineFileType(url)
    const virtualFile = { name: url, size: '-', type: fileType }
    updateFiles(virtualFile)
    urlInputRef.current.value = ''
  }

  const handleDrop = (event) => {
    event.preventDefault()
    event.stopPropagation()

    if (disabled) {
      return
    }

    if (event.dataTransfer.files && event.dataTransfer.files[0]) {
      updateFiles(event.dataTransfer.files)
    }

    setDragActive(false)
  }

  const removeFile = (targetFile) => {
    const fileIndex = Array.from(files || []).findIndex(
      (file) => file.name === targetFile.name
    )

    if (fileIndex === -1) {
      return
    }

    const updatedFiles = Array.from(files || []).filter(
      (file) => file.name !== targetFile.name
    )

    const updatedFileTypes = Array.isArray(value) ? [...value] : []
    updatedFileTypes.splice(fileIndex, 1)

    onChange({ target: { name: name, files: updatedFiles } })
    onChange({ target: { name: name, value: updatedFileTypes } })
  }

  const handleFileTypeChange = (event, index) => {
    const { name, value: dropdownValue } = event.target
    const updatedFileTypes = Array.isArray(value) ? [...value] : []
    updatedFileTypes[index] = {
      ...updatedFileTypes[index],
      fileType: dropdownValue,
    }

    onChange({ target: { name: name, value: updatedFileTypes } })
  }

  const determineFileType = (url) => {
    if (url.includes('drive.google.com')) {
      return 'url/drive'
    }
    const extensionMatch = url.match(/\.(pdf|png|jpeg|jpg|docx|pptx|xlsx)$/)
    if (extensionMatch) {
      const extension = extensionMatch[1]
      switch (extension) {
        case 'pdf':
          return 'application/pdf'
        case 'png':
          return 'image/png'
        case 'jpeg':
        case 'jpg':
          return 'image/jpeg'
        case 'docx':
          return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        case 'pptx':
          return 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
        case 'xlsx':
          return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        default:
          return 'application/octet-stream'
      }
    }
    return 'application/octet-stream'
  }

  return (
    <div>
      <Toast ref={toastRef} />
      <div className={styles.fileUploadContainer} onDragEnter={handleDrag}>
        <input
          type="file"
          ref={inputRef}
          className={styles.inputFileUpload}
          onChange={handleFileUpload}
          multiple={true}
        />
        <label className={styles.labelFileUpload} disabled={disabled}>
          <div>
            <button
              className={styles.uploadButton}
              onClick={handleButtonClick}
              disabled={disabled}
            >
              {`${isMobile ? '' : 'Drag Files Here Or'} Click To Upload`}
            </button>
            <div style={{ display: 'flex', marginTop: '10px' }}>
              <InputText
                type="text"
                ref={urlInputRef}
                placeholder="Paste file URL here"
                disabled={disabled}
                style={{ marginRight: '10px', width: '100%' }}
              />
              <Button
                style={{ width: '18%', height: '48px', borderRadius: '6px' }}
                icon={<Image src={Backarrow} alt="go" width={60} height={60} />}
                onClick={handleUrlSubmit}
                rounded
                disabled={disabled}
              />
            </div>
          </div>
        </label>
        {dragActive && (
          <div
            className={styles.dragFileElement}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          ></div>
        )}
      </div>
      <div className="mt-3">
        <FileTableHeader />
        <FileTable
          name={name}
          value={value}
          files={files ?? []}
          removeFile={removeFile}
          fileTypes={fileTypes}
          disabled={disabled}
          onFileTypeChange={handleFileTypeChange}
        />
      </div>
    </div>
  )
}

const FileTableHeader = () => {
  return (
    <FileTableHeaderContainer>
      <FileTableHeaderColumn width={'45%'} label="File Name" />
      <FileTableHeaderColumn width={'15%'} label="Size" />
      <FileTableHeaderColumn width={'35%'} label="File Type" />
      <FileTableHeaderColumn width={'5%'} label="" />
    </FileTableHeaderContainer>
  )
}

const FileTableHeaderContainer = ({ children }) => {
  return <div className={styles.fileTableHeaderContainer}>{children}</div>
}

const FileTableHeaderColumn = ({ label, width }) => {
  return (
    <div className={styles.fileTableHeaderColumn} style={{ width: width }}>
      {label}
    </div>
  )
}

const FileTable = ({
  name,
  value,
  files,
  removeFile,
  fileTypes,
  disabled,
  onFileTypeChange,
}) => {
  return (
    <FileTableContainer>
      {Array.from(
        !disabled
          ? files?.length
            ? files
            : value?.length
              ? value
              : []
          : (value ?? [])
      ).map((file, index) => (
        <FileRow
          name={name}
          key={index}
          value={(value && value[index]) || 'undefined'}
          index={index}
          file={file}
          onDelete={removeFile}
          fileTypes={fileTypes}
          disabled={disabled}
          onFileTypeChange={onFileTypeChange}
        />
      ))}
    </FileTableContainer>
  )
}

const FileTableContainer = ({ children }) => {
  return <div className={styles.fileTableContainer}>{children}</div>
}

const FileRow = ({
  name,
  file,
  value,
  onDelete,
  fileTypes,
  disabled,
  onFileTypeChange,
  index,
}) => {
  const downloadFile = async (file, fileName) => {
    try {
      const apiUrl = `${process.env.NEXT_PUBLIC_FORM_BUILDER_API}FormMetadata/file/${encodeURIComponent(file.guid)}`
      const response = await fetch(apiUrl)

      if (response.ok) {
        const blob = await response.blob()
        saveAs(blob, `${fileName}`)
      } else {
        console.error(`Error while downloading the file: ${response}`)
      }
    } catch (error) {
      console.error(`Error while downloading the file: ${error.message}`)
    }
  }

  const bytesToSize = (bytes) => {
    if (bytes === '-' || bytes === undefined) return '-'

    if (bytes === 0) return '0 MB'
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)), 10)
    return `${(bytes / 1024 ** i).toFixed(2)} ${sizes[i]}`
  }

  const shortenUrl = (url, maxLen = 100) => {
    if (url?.length <= maxLen) return url

    const startChars = Math.ceil(maxLen / 2)
    return `${url?.substring(0, startChars)}...`
  }

  const displayFile = () => {
    return disabled && file?.size
  }

  const isMaskedValue = value?.fileType === '●●●●●●●●'

  return (
    <FileRowContainer>
      <div className={styles.fileRowLabel} style={{ width: '45%' }}>
        {shortenUrl(
          disabled || value?.length?.$numberInt ? value.fileName : file.name
        )}
      </div>
      <div className={styles.fileRowLabel} style={{ width: '15%' }}>
        {bytesToSize(
          disabled || value?.length?.$numberInt
            ? value.length?.$numberInt
            : file.size
        )}
      </div>
      <div className={styles.fileRowLabel} style={{ width: '35%' }}>
        <Dropdown
          name={`${name}`}
          value={isMaskedValue ? '●●●●●●●●' : value.fileType}
          options={
            isMaskedValue
              ? [{ label: '●●●●●●●●', value: '●●●●●●●●' }]
              : fileTypes
          }
          style={{ width: '75%', textAlign: 'left' }}
          disabled={disabled || isMaskedValue}
          onChange={(e) => onFileTypeChange(e, index)}
        />
      </div>
      <div
        className={styles.fileRowLabel}
        style={{ textAlign: 'right', width: '5%' }}
      >
        {!disabled && <Tooltip target=".iconTooltip" />}
        {!disabled && (
          <span
            className={`pi pi-trash iconTooltip`}
            style={{ color: 'red' }}
            onClick={() => onDelete(file)}
          />
        )}
        <span
          className={`pi ml-2 pi-download iconTooltip`}
          style={{ color: 'blue' }}
          onClick={() => downloadFile(file, file.fileName)}
        />
      </div>
    </FileRowContainer>
  )
}

const FileRowContainer = ({ children }) => {
  return <div className={styles.fileRowContainer}>{children}</div>
}
