import { useContext, useRef, useState } from 'react'
import { Dropdown } from 'primereact/dropdown'
import { Toolt<PERSON> } from 'primereact/tooltip'
import { saveAs } from 'file-saver'
import { Button } from 'primereact/button'
import styles from './FileUpload.module.css'
import { InputText } from 'primereact/inputtext'
import { Toast } from 'primereact/toast'
import useUtilityFunctions from '../../../../../hooks/useUtilityFunctions'
import UserProfileContext from '../../../../../public/UserProfileContext/UserProfileContext'
import replaceIcon from '../../../../../svg/WorkflowCanvas/replacement.svg'
import Image from 'next/image'
import Backarrow from '../../../../../svg/backArrow_right.svg'

// Ahmet: This component is pretty complicated, message to me if you have any questions about
export const FileInput = ({
  name,
  disabled,
  fileTypes,
  files,
  value,
  onChange,
  isMobile = false,
  isSubmissionPage,
  isRevision = false,
  localFiles,
  setLocalFiles,
  fileMetadata,
  setFileMetadata,
}) => {
  const [dragActive, setDragActive] = useState(false)
  const inputRef = useRef(null)
  const toastRef = useRef(null)
  const urlInputRef = useRef(null)
  const { createUserFriendlyDate } = useUtilityFunctions()
  const userProfile = useContext(UserProfileContext)
  const [canUpdated, setCanUpdated] = useState(true)

  const handleButtonClick = (replaceIndex = -1) => {
    if (disabled) {
      return
    }
    console.log('onBlend', replaceIndex)
    inputRef.current.dataset.isReplace = replaceIndex
    inputRef.current.click()
  }

  const handleDrag = (event) => {
    event.preventDefault()
    event.stopPropagation()

    if (disabled) {
      return
    }

    if (event.type === 'dragenter' || event.type === 'dragover') {
      setDragActive(true)
    } else if (event.type === 'dragleave') {
      setDragActive(false)
    }
  }

  const updateFiles = (addedFiles, replaceIndex) => {
    if (isRevision) {
      const currentFiles = Array.from(files || [])
      let newFiles

      if (addedFiles instanceof FileList) {
        newFiles = Array.from(addedFiles)
      } else {
        newFiles = Array.isArray(addedFiles) ? addedFiles : [addedFiles]
      }

      const uniqueNewFiles = newFiles.filter(
        (newFile) =>
          !currentFiles.some((currentFile) => currentFile.name === newFile.name)
      )

      console.log('test on', uniqueNewFiles)
      setLocalFiles((prev) => {
        const arr = [...prev]
        if (replaceIndex >= 0) {
          arr[replaceIndex] = newFiles[0]
          return arr
        } else {
          return uniqueNewFiles
        }
      })
      setFileMetadata((prev) => {
        let localValue = [...prev]
        if (replaceIndex >= 0) {
          localValue[replaceIndex] = {
            ...localValue[replaceIndex],
            fileName: newFiles[0].name,
            updatedTime: new Date(Date.now()),
          }
          console.log('onReplace', localValue, newFiles[0].name)
        } else {
          uniqueNewFiles.forEach((file) => {
            localValue.push({
              fileType: '',
              fileName: file.name,
              updatedBy: userProfile?.displayName,
              updatedTime: new Date(Date.now()),
              userID: `${userProfile?.userID}`,
              version: '1.0',
            })
          })
        }
        // console.log("onReplace", localValue, newFiles[0].name)
        return localValue
      })
      return
    }
    if (!isSubmissionPage) {
      const currentValue = [...(value || [])]
      let newFiles
      if (addedFiles instanceof FileList) {
        newFiles = Array.from(addedFiles)
      } else {
        newFiles = Array.isArray(addedFiles) ? addedFiles : [addedFiles]
      }
      if (replaceIndex >= 0) {
        currentValue[replaceIndex] = {
          ...currentValue[replaceIndex],
          fileName: newFiles[0].name,
          updatedBy: userProfile?.displayName,
          updatedTime: new Date(Date.now()),
          userID: `${userProfile?.userID}`,
          version: '1.0',
        }
      } else {
        newFiles.forEach((file) => {
          currentValue.push({
            fileType: '',
            fileName: file.name,
            updatedBy: userProfile?.displayName,
            updatedTime: new Date(Date.now()),
            userID: `${userProfile?.userID}`,
            version: '1.0',
          })
        })
      }

      onChange({ target: { name: name, files: newFiles } })
      onChange({ target: { name: name, value: currentValue } })
    }

    const currentFiles = Array.from(files || [])
    let newFiles

    if (addedFiles instanceof FileList) {
      newFiles = Array.from(addedFiles)
    } else {
      newFiles = Array.isArray(addedFiles) ? addedFiles : [addedFiles]
    }

    const uniqueNewFiles = newFiles.filter(
      (newFile) =>
        !currentFiles.some((currentFile) => currentFile.name === newFile.name)
    )
    const updatedFileTypes = Array.isArray(value) ? [...value] : []

    let combinedFiles
    if (replaceIndex >= 0) {
      combinedFiles = [...currentFiles]
      if (combinedFiles.some((file) => file.name === newFiles[0].name))
        return alert('already exist in file list')
      combinedFiles[replaceIndex] = newFiles[0]
      updatedFileTypes[replaceIndex] = {
        ...updatedFileTypes[replaceIndex],
        fileName: newFiles[0].name,
        updatedBy: userProfile?.displayName,
        updatedTime: new Date(Date.now()),
        userID: `${userProfile?.userID}`,
        version:
          canUpdated && updatedFileTypes[replaceIndex].guid
            ? `${(
                parseInt(updatedFileTypes[replaceIndex]?.version) + 1
              ).toFixed(1)}`
            : `${updatedFileTypes[replaceIndex].version}`,
      }
      setCanUpdated(false)
    } else {
      combinedFiles = [...currentFiles, ...uniqueNewFiles]
      uniqueNewFiles.forEach((file) => {
        updatedFileTypes.push({
          fileType: '',
          fileName: file.name,
          updatedBy: userProfile?.displayName,
          updatedTime: new Date(Date.now()),
          userID: `${userProfile?.userID}`,
          version: '1.0',
        })
      })
    }

    onChange({ target: { name: name, files: combinedFiles } })
    onChange({ target: { name: name, value: updatedFileTypes } })
  }

  const handleFileUpload = (event) => {
    event.preventDefault()
    event.stopPropagation()

    if (disabled) {
      return
    }

    console.log('isReplace', parseInt(inputRef.current.dataset.isReplace))

    if (event.target.files && event.target.files.length > 0) {
      updateFiles(
        event.target.files,
        parseInt(inputRef.current.dataset.isReplace)
      )
      event.target.value = null
    }
  }
  const isValidUrl = (url) => {
    try {
      new URL(url)
      return true
    } catch (_) {
      return false
    }
  }

  const handleUrlSubmit = () => {
    const url = urlInputRef.current.value

    if (url.trim() === '') {
      toastRef.current.show({
        severity: 'warn',
        summary: 'URL Required',
        detail: 'Please enter a URL.',
        life: 3000,
      })
      return
    }

    if (!url.startsWith('https://')) {
      toastRef.current.show({
        severity: 'error',
        summary: 'Invalid URL',
        detail: 'Only https URLs are allowed.',
        life: 3000,
      })
      return
    }

    if (!isValidUrl(url)) {
      toastRef.current.show({
        severity: 'error',
        summary: 'Invalid URL',
        detail: 'Please provide a valid URL.',
        life: 3000,
      })
      return
    }

    const fileType = determineFileType(url)
    const virtualFile = { name: url, size: '-', type: fileType }
    updateFiles(virtualFile)
    urlInputRef.current.value = ''
  }

  const handleDrop = (event) => {
    event.preventDefault()
    event.stopPropagation()

    if (disabled) {
      return
    }

    if (event.dataTransfer.files && event.dataTransfer.files[0]) {
      updateFiles(event.dataTransfer.files)
    }

    setDragActive(false)
  }

  const removeFile = (targetFile, targetValue, index) => {
    if (isRevision) {
      setLocalFiles((prev) => {
        const arr = [...prev]
        arr.splice(index, 1)
        return arr
      })
      return setFileMetadata((prev) => {
        const arr = [...prev]
        arr.splice(index, 1)
        return arr
      })
    }

    const fileIndex = Array.from(files || []).findIndex(
      (file) => file.name === targetFile.name
    )
    const valueIndex = Array.from(value || []).findIndex(
      (val) => val.fileName === targetValue.fileName
    )

    const updatedFiles = Array.from(files || []).filter(
      (file) => file.name !== targetFile.name
    )

    const updatedFileTypes = Array.isArray(value) ? [...value] : []

    updatedFileTypes.splice(valueIndex, 1)

    onChange({ target: { name: name, files: updatedFiles } })
    onChange({ target: { name: name, value: updatedFileTypes } })
  }

  const replaceFile = (targetIndex) => {
    handleButtonClick(targetIndex)
  }

  const handleFileTypeChange = (event, index) => {
    const { name, value: dropdownValue } = event.target
    if (isRevision) {
      return setFileMetadata((prev) => {
        const data = [...prev]
        data[index].fileType = dropdownValue
        return data
      })
    } else {
      const updatedFileTypes = Array.isArray(value) ? [...value] : []
      updatedFileTypes[index] = {
        ...updatedFileTypes[index],
        fileType: dropdownValue,
        version: '1.0',
        updatedTime: new Date(Date.now()),
        updatedBy: userProfile?.displayName,
        userID: `${userProfile.userID}`,
      }
      onChange({ target: { name: name, value: updatedFileTypes } })
    }
  }

  const determineFileType = (url) => {
    if (url.includes('drive.google.com')) {
      return 'url/drive'
    }
    const extensionMatch = url.match(/\.(pdf|png|jpeg|jpg|docx|pptx|xlsx)$/)
    if (extensionMatch) {
      const extension = extensionMatch[1]
      switch (extension) {
        case 'pdf':
          return 'application/pdf'
        case 'png':
          return 'image/png'
        case 'jpeg':
        case 'jpg':
          return 'image/jpeg'
        case 'docx':
          return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        case 'pptx':
          return 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
        case 'xlsx':
          return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        default:
          return 'application/octet-stream'
      }
    }
    return 'application/octet-stream'
  }

  return (
    <div>
      <Toast ref={toastRef} />
      <div className={styles.fileUploadContainer} onDragEnter={handleDrag}>
        <input
          type="file"
          ref={inputRef}
          className={styles.inputFileUpload}
          onChange={handleFileUpload}
          multiple={true}
        />
        <label className={styles.labelFileUpload} disabled={disabled}>
          <div>
            <button
              className={styles.uploadButton}
              onClick={() => handleButtonClick(-1)}
              disabled={disabled}
            >
              {`${isMobile ? '' : 'Drag Files Here Or'} Click To Upload`}
            </button>
            <div style={{ display: 'flex', marginTop: '10px' }}>
              <InputText
                type="text"
                ref={urlInputRef}
                placeholder="Paste file URL here"
                disabled={disabled}
                style={{ marginRight: '10px', width: '100%' }}
              />
              <Button
                style={{ width: '18%', height: '48px', borderRadius: '6px' }}
                icon={<Image src={Backarrow} alt="go" width={60} height={60} />}
                onClick={handleUrlSubmit}
                rounded
                disabled={disabled}
              />
            </div>
          </div>
        </label>
        {dragActive && (
          <div
            className={styles.dragFileElement}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          ></div>
        )}
      </div>
      <div className="mt-3">
        <FileTableHeader />
        <FileTable
          name={name}
          value={value}
          files={files ?? []}
          removeFile={removeFile}
          replaceFile={replaceFile}
          fileTypes={fileTypes}
          disabled={disabled}
          onFileTypeChange={handleFileTypeChange}
          isSubmissionPage={isSubmissionPage}
          localFiles={localFiles}
          fileMetadata={fileMetadata}
          isRevision={isRevision}
          isValidUrl={isValidUrl}
        />
      </div>
    </div>
  )
}

const FileTableHeader = () => {
  return (
    <FileTableHeaderContainer>
      <FileTableHeaderColumn width={'16.6%'} label="File Type" />
      <FileTableHeaderColumn width={'16.6%'} label="File Name" />
      <FileTableHeaderColumn width={'16.6%'} label="Version" />
      <FileTableHeaderColumn width={'16.6%'} label="Uploaded By" />
      <FileTableHeaderColumn width={'16.6%'} label="Uploaded Date" />
      <FileTableHeaderColumn
        width={'16.6%'}
        textAlign={'right'}
        label="Actions"
      />
    </FileTableHeaderContainer>
  )
}

const FileTableHeaderContainer = ({ children }) => {
  return <div className={styles.fileTableHeaderContainer}>{children}</div>
}

const FileTableHeaderColumn = ({ label, width, textAlign }) => {
  return (
    <div
      className={styles.fileTableHeaderColumn}
      style={{ width: width, textAlign: textAlign }}
    >
      {label}
    </div>
  )
}

const FileTable = ({
  name,
  value,
  files,
  removeFile,
  replaceFile,
  fileTypes,
  disabled,
  onFileTypeChange,
  isSubmissionPage,
  localFiles,
  fileMetadata,
  isRevision,
  isValidUrl,
}) => {
  return (
    <FileTableContainer>
      {/* {`${value?.length === files?.length ? index : (value?.length - files?.length) - 2}`} */}
      {isRevision
        ? fileMetadata?.map((fileOption, index) => {
            return (
              <FileRow
                name={name}
                key={index}
                value={fileOption}
                index={index}
                file={localFiles[index]}
                onDelete={removeFile}
                replaceFile={replaceFile}
                fileTypes={fileTypes}
                disabled={disabled}
                onFileTypeChange={onFileTypeChange}
                isSubmissionPage={isSubmissionPage}
                isValidUrl={isValidUrl}
              />
            )
          })
        : value &&
          Array.isArray(value) &&
          value?.map((fileOption, index) => {
            return (
              <FileRow
                name={name}
                key={index}
                value={fileOption}
                index={index}
                file={
                  files[
                    value?.length === files?.length
                      ? index
                      : value?.length - files?.length - 2
                  ]
                }
                onDelete={removeFile}
                replaceFile={replaceFile}
                fileTypes={fileTypes}
                disabled={disabled}
                onFileTypeChange={onFileTypeChange}
                isSubmissionPage={isSubmissionPage}
                isValidUrl={isValidUrl}
              />
            )
          })}
      {/* {Array.from(!disabled ? files?.length ? files : (value?.length ? value : []) : value ?? []).map((file, index) => (
                <FileRow name={name} key={index} value={(value && value[index]) || 'undefined'} index={index} file={file} onDelete={removeFile} replaceFile={replaceFile} fileTypes={fileTypes} disabled={disabled} onFileTypeChange={onFileTypeChange} permission={permission} isSubmissionPage={isSubmissionPage} />
            ))} */}
    </FileTableContainer>
  )
}

const FileTableContainer = ({ children }) => {
  return <div className={styles.fileTableContainer}>{children}</div>
}

const FileRow = ({
  name,
  file,
  value,
  onDelete,
  replaceFile,
  fileTypes,
  disabled,
  onFileTypeChange,
  index,
  isSubmissionPage,
  isValidUrl,
}) => {
  const { createUserFriendlyDate } = useUtilityFunctions()
  const userProfile = useContext(UserProfileContext)

  const downloadFile = async (value, file) => {
    try {
      console.log('onDownload', value, file)
      if (value.guid) {
        const apiUrl = `${
          process.env.NEXT_PUBLIC_FORM_BUILDER_API
        }FormMetadata/file/${encodeURIComponent(value.guid)}`
        const response = await fetch(apiUrl)

        if (response.ok) {
          const blob = await response.blob()
          const fileBlob = new Blob([blob], { type: `${value.contentType}` })
          saveAs(fileBlob, `${value.fileName}`)
        } else {
          alert(`Error while downloading the file: ${response}`)
        }
      } else {
        const fileBlob = new Blob([file], { type: file.type })
        saveAs(fileBlob, `${file.name}`)
      }
    } catch (error) {
      console.error(`Error while downloading the file: ${error.message}`)
    }
  }

  const ViewFile = ({ value, file }) => {
    return (
      <a
        target={'_blank'}
        style={{ cursor: 'pointer' }}
        onClick={() => {
          console.log('test onClick', value, file)
          if (value.guid) {
            ;(async function () {
              const apiUrl = `${
                process.env.NEXT_PUBLIC_FORM_BUILDER_API
              }FormMetadata/file/${encodeURIComponent(value.guid)}`
              const response = await fetch(apiUrl)
              if (response.ok) {
                const blob = await response.blob()
                const fileBlob = new Blob([blob], {
                  type: value.type ?? `${value.contentType}`,
                })
                const url = window.URL.createObjectURL(fileBlob)
                window.open(url)
              }
            })()
          } else {
            const fileUrl = value.fileName
            if (fileUrl && isValidUrl(fileUrl)) {
              window.open(fileUrl)
            } else {
              const fileBlob = new Blob([file], {
                type: file.type ?? `${value.contentType}`,
              })
              const url = window.URL.createObjectURL(fileBlob)
              window.open(url)
            }
          }
        }}
      >
        {value?.fileName ?? value?.name}{' '}
      </a>
    )

    // if (file.guid) {
    //     const apiUrl = `${process.env.NEXT_PUBLIC_FORM_BUILDER_API}FormMetadata/file/${encodeURIComponent(file.guid)}`
    //     const response = await fetch(apiUrl)
    //     if (response.ok) {
    //         const blob = await response.blob()
    //         const fileBlob = new Blob([blob], { type: file.type ?? `${value.contentType}` })
    //         const url = window.URL.createObjectURL(fileBlob)
    //         return <a href={url} target={"_blank"} >{file.name}</a>
    //     } else {
    //         console.error(
    //             `Error while downloading the file: ${response}`
    //         )
    //         return <p>file.name</p>
    //     }
    // } else {
    //     const url = window.URL.createObjectURL(file)
    //     return <a href={url} target={"_blank"} >{file.name}</a>

    // }
  }

  const bytesToSize = (bytes) => {
    if (bytes === '-' || bytes === undefined) return '-'

    if (bytes === 0) return '0 MB'
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)), 10)
    return `${(bytes / 1024 ** i).toFixed(2)} ${sizes[i]}`
  }

  const shortenUrl = (url, maxLen = 100) => {
    if (url?.length <= maxLen) return url

    const startChars = Math.ceil(maxLen / 2)
    return `${url?.substring(0, startChars)}...`
  }

  const displayFile = () => {
    return disabled && file?.size
  }

  // console.log('display', file, value)

  const ActionButton = ({ file, value, index }) => {
    if (isSubmissionPage) {
      return (
        <div className="flex align-items-center justify-content-end">
          {`${userProfile?.userID}` === value?.userID && (
            <span
              title="Delete"
              className={`pi pi-trash iconTooltip cursor-pointer`}
              style={{ color: 'red' }}
              onClick={() => onDelete(file, value, index)}
            />
          )}
          {`${userProfile?.userID}` === value?.userID && (
            <span
              title="Replace"
              className={`pi ml-2  iconTooltip cursor-pointer `}
              style={{ color: 'blue' }}
              onClick={() => replaceFile(index)}
            >
              <Image src={replaceIcon} width={30} height={30} />
            </span>
          )}
          <span
            title={'Download'}
            className={`pi ml-2 pi-download iconTooltip cursor-pointer`}
            style={{ color: 'blue' }}
            onClick={() => downloadFile(value, file)}
          />
        </div>
      )
    }

    if (disabled)
      return (
        <span
          title={'Download'}
          className={`pi ml-2 pi-download iconTooltip cursor-pointer`}
          style={{ color: 'blue' }}
          onClick={() => downloadFile(value, file)}
        />
      )

    if (!disabled)
      // `${userProfile?.userID}` === value?.userID - This line needs to be changed because, everyone who has a permission needs to be able to see trash icon. but only the user can see.
      return (
        <div className="flex align-items-center justify-content-end">
          {`${userProfile?.userID}` === value?.userID && (
            <span
              title={'Delete'}
              className={`pi pi-trash iconTooltip cursor-pointer`}
              style={{ color: 'red' }}
              onClick={() => onDelete(file, value, index)}
            />
          )}
          {`${userProfile?.userID}` === value?.userID && (
            <span
              title={'Replace'}
              className={`pi ml-2 iconTooltip cursor-pointer flex align-items-center`}
              style={{ color: 'blue' }}
              onClick={() => replaceFile(index)}
            >
              <Image src={replaceIcon} width={30} height={30} />
            </span>
          )}
          <span
            title={'Download'}
            className={`pi ml-2 pi-download iconTooltip cursor-pointer`}
            style={{ color: 'blue' }}
            onClick={() => downloadFile(value, file)}
          />
        </div>
      )
  }
  const isMaskedValue = value?.fileType === '●●●●●●●●'

  return (
    <FileRowContainer>
      <div className={styles.fileRowLabel} style={{ width: '16.6%' }}>
        {/* {`${(disabled || `${userProfile?.userID}` !== value?.userID)}`} */}
        <Dropdown
          style={{ width: '100%' }}
          name={`${name}`}
          value={isMaskedValue ? '●●●●●●●●' : value?.fileType}
          options={
            isMaskedValue
              ? [{ label: '●●●●●●●●', value: '●●●●●●●●' }]
              : fileTypes
          }
          disabled={
            disabled ||
            `${userProfile?.userID}` !== value?.userID ||
            isMaskedValue
          }
          onChange={(e) => onFileTypeChange(e, index)}
        />
      </div>
      <div className={styles.fileRowLabel} style={{ width: '16.6%' }}>
        {<ViewFile value={value} file={file} />}
      </div>
      <div className={styles.fileRowLabel} style={{ width: '16.6%' }}>
        {value?.version ?? '1.0'}
      </div>
      <div className={styles.fileRowLabel} style={{ width: '16.6%' }}>
        {value?.updatedBy ?? userProfile?.displayName}
      </div>
      <div className={styles.fileRowLabel} style={{ width: '16.6%' }}>
        {createUserFriendlyDate(value?.updatedTime ?? new Date())}
      </div>
      <div
        className={styles.fileRowLabel}
        style={{ textAlign: 'right', width: '16.6%' }}
      >
        <ActionButton file={file} value={value} index={index} />
        {/* {`${permission} ${`${userProfile?.userID}` === value?.userID} ${userProfile?.userID} ${value?.userID}`} */}
        {/* {!disabled && <Tooltip target='.iconTooltip' />} */}
        {/* {
                    `${userProfile?.userID}` === value?.userID ?
                        <>
                            {!disabled && <span className={`pi pi-trash iconTooltip`} style={{ color: 'red' }} onClick={() => onDelete(file, value)} />}
                            <span className={`pi ml-2 pi-replay iconTooltip`} style={{ color: 'blue' }} onClick={() => replaceFile(index)} />
                            <span className={`pi ml-2 pi-download iconTooltip`} style={{ color: 'blue' }} onClick={() => downloadFile(value, file)} />
                        </> :
                        <>
                            {
                                (permission === "Read Only" && `${userProfile?.userID}` === value?.userID) &&
                                <>
                                    {!disabled && <span className={`pi pi-trash iconTooltip`} style={{ color: 'red' }} onClick={() => onDelete(file, value)} />}
                                    <span className={`pi ml-2 pi-replay iconTooltip`} style={{ color: 'blue' }} onClick={() => replaceFile(index)} />
                                    <span className={`pi ml-2 pi-download iconTooltip`} style={{ color: 'blue' }} onClick={() => downloadFile(value, file)} />
                                </>
                            }
                            {
                                (permission === "Editable" && `${userProfile?.userID}` === value?.userID) &&
                                <>
                                    <span className={`pi ml-2 pi-download iconTooltip`} style={{ color: 'blue' }} onClick={() => downloadFile(value, file)} />
                                </>
                            }
                        </>
                } */}
      </div>
    </FileRowContainer>
  )
}

const FileRowContainer = ({ children }) => {
  return <div className={styles.fileRowContainer}>{children}</div>
}
