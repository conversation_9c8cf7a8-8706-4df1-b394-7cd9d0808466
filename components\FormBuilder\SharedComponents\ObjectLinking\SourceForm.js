import clsx from 'clsx'
import useUtilityFunctions from '../../../../hooks/useUtilityFunctions'
import { SettingsLabel } from '../../Settings/UI/SettingsLabel/SettingsLabel'
import { Dropdown } from 'primereact/dropdown'

import styles from '../../../../styles/BuilderSwimLaneStyles/SettingsMenu.module.css'
import { useApi } from '../../../../hooks/useApi'
import { SolutionIds } from '../../../../src/contants'

export const SourceForm = ({
  inputs,
  sourceFormOptionsLabels,
  handleInputChange,
  sourceFormOptions,
  isSpreadsheet = false,
  solutionId = 1,
  callApi,
  loading,
}) => {
  const { createTargetObject } = useUtilityFunctions()

  const clearFormFields = () => {
    const fields = [
      ['primarySearchField', null],
      ['resultFields', null],
      ['selectedDataTableColumns', []],
      ['dataTableColumns', []],
    ]

    fields.forEach(([field, value]) => {
      handleInputChange(createTargetObject(`${inputs?.guid}.${field}`, value))
    })
  }

  console.log('inputs', inputs)

  return (
    <>
      <SettingsLabel
        label={isSpreadsheet ? 'Source Table' : 'Source Form'}
        isRequired={true}
      />
      <Dropdown
        className={clsx(
          styles.fullWidth,
          'p-tempStyle',
          isSpreadsheet && !inputs?.isObjectLinking && styles.disabledField
        )}
        name={`${inputs?.guid}.sourceFormName`}
        value={inputs?.sourceFormName ?? ''}
        options={sourceFormOptionsLabels}
        onChange={async (e) => {
          handleInputChange(e)
          const selectedOption = sourceFormOptions.find(
            (option) => option.label === e.value
          )

          if (selectedOption) {
            let data = selectedOption.data
            if (isSpreadsheet) {
              console.log(selectedOption.url, 'selectedOption')
              const result = await callApi({
                method: 'GET',
                url: selectedOption.url,
              })
              if (!result || result?.data?.status === false) {
                return alert(result?.data?.message)
              }
              data = Object.keys(
                result?.data?.data?.result?.[0]?.itemdata ?? []
              ).map((key) => ({
                // id: key,
                label: key,
                name: key,
                wholeData: result?.data?.data?.result,
                displayName: key,
              }))
            }
            handleInputChange(
              createTargetObject(`${inputs?.guid}.sourceFormData`, {
                id: selectedOption.id,
                data: data,
              })
            )
            // handleInputChange(
            //   createTargetObject(`${inputs?.guid}.primarySearchField`, null)
            // )
            // handleInputChange(
            //   createTargetObject(`${inputs?.guid}.resultFields`, [])
            // )
            // handleInputChange(
            //   createTargetObject(`${inputs?.guid}.dataTableColumns`, [])
            // )
            // handleInputChange(
            //   createTargetObject(`${inputs?.guid}.selectedDataTableColumns`, [])
            // )
          }
          clearFormFields()
        }}
        filter
        disabled={isSpreadsheet && !inputs?.isObjectLinking}
        loading={loading}
      />
    </>
  )
}
