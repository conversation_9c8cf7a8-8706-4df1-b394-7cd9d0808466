.certificateBuilderContainer {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 2rem;
  background: #f9fbfd;
  border-radius: 12px;
  box-shadow: 0 2px 8px #0001;
}

.templatePickerCard {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 1px 4px #0001;
  padding: 0.75rem 0.5rem;
  margin-bottom: 0.5rem;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.templatePickerTitle {
  font-size: 0.98rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.templatePickerList {
  display: flex;
  gap: 0.75rem;
  width: 100%;
}

.templateCard {
  background: #f3f4f6;
  border-radius: 10px;
  border: 2px solid #e5e7eb;
  box-shadow: 0 1px 2px #0001;
  cursor: pointer;
  padding: 0.25rem 0.25rem 0.4rem 0.25rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 70px;
  transition: border 0.2s, box-shadow 0.2s;
}

.templateCardSelected {
  composes: templateCard;
  border: 3px solid #2563eb;
  box-shadow: 0 0 0 2px #2563eb33;
  background: #e0e7ff;
}

.templateCardName {
  font-size: 11px;
  margin-top: 4px;
  text-align: center;
  color: #374151;
}

.cardsRowWrapper {
  display: flex;
  flex-direction: row;
  gap: 2rem;
  width: 100%;
}

.editorCard,
.previewCard {
  flex: 1;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 1px 4px #0001;
  padding: 1.5rem 1.5rem 2rem 1.5rem;
  min-width: 0;
  display: flex;
  flex-direction: column;
}

.cardTitle {
  font-size: 1.05rem;
  font-weight: 500;
  margin-bottom: 1rem;
}

.emptyPreview {
  color: #aaa;
  font-style: italic;
  padding: 2rem 0;
  text-align: center;
}

.downloadPdfBtn {
  background: #2563eb;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 0.35em 0.9em;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.18s;
  margin-left: 1rem;
  box-shadow: 0 1px 2px #0001;
}
.downloadPdfBtn:hover,
.downloadPdfBtn:focus {
  background: #1d4ed8;
}

@media (max-width: 900px) {
  .cardsRowWrapper {
    flex-direction: column;
    gap: 1rem;
  }
  .editorCard,
  .previewCard {
    margin: 0;
    padding: 1rem;
  }
  .templatePickerCard {
    padding: 0.5rem;
  }
}
