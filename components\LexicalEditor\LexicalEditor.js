'use client'

import Theme from './Themes/Theme'
import { LexicalComposer } from '@lexical/react/LexicalComposer'
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin'
import { ContentEditable } from '@lexical/react/LexicalContentEditable'
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin'
import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary'
import ToolbarPlugin from './Plugins/ToolbarPlugin/ToolbarPlugin'
import { HeadingNode, QuoteNode } from '@lexical/rich-text'
import { OnChangePlugin } from '@lexical/react/LexicalOnChangePlugin'
import { TableCellNode, TableNode, TableRowNode } from '@lexical/table'
import { ListItemNode, ListNode } from '@lexical/list'
import { CodeHighlightNode, CodeNode } from '@lexical/code'
import { AutoLinkNode, LinkNode } from '@lexical/link'
import { LinkPlugin } from '@lexical/react/LexicalLinkPlugin'
import { ListPlugin } from '@lexical/react/LexicalListPlugin'
import { MarkdownShortcutPlugin } from '@lexical/react/LexicalMarkdownShortcutPlugin'
import { TRANSFORMERS } from '@lexical/markdown'
import ListMaxIndentLevelPlugin from './Plugins/ListMaxIndentLevelPlugin/ListMaxIndentLevelPlugin'
import CodeHighlightPlugin from './Plugins/CodeHighlightPlugin/CodeHighlightPlugin'
import AutoLinkPlugin from './Plugins/AutoLinkPlugin/AutoLinkPlugin'
import MentionsPlugin from './Plugins/MentionsPlugin'
import styles from './LexicalEditor.module.css'
import clsx from 'clsx'
import { MentionNode } from './nodes/MentionNode'
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext'
import { useEffect } from 'react'
import { ImageNode } from './nodes/ImageNode'
import ImagesPlugin from './Plugins/ImagesPlugin/ImagesPlugin'
import { EditorRefPlugin } from './Plugins/EditorRefPlugin/EditorRefPlugin'

function Placeholder() {
  return <div className={styles['editor-placeholder']}></div>
}

export const lexicalDefaultValue = JSON.stringify({
  root: {
    children: [
      {
        children: [
          {
            detail: 0,
            format: 0,
            mode: 'normal',
            style: '',
            text: '',
            type: 'text',
            version: 1
          }
        ],
        direction: 'ltr',
        format: '',
        indent: 0,
        type: 'paragraph',
        version: 1
      }
    ],
    direction: 'ltr',
    format: '',
    indent: 0,
    type: 'root',
    version: 1
  }
})

export default function Editor({ value, id, name, onChange, className, readOnly = false, mentionsLists, onEditorReady }) {
  const editorConfig = {
    theme: Theme,
    onError(error) {
      throw error
    },
    nodes: [
      HeadingNode,
      ListNode,
      ListItemNode,
      QuoteNode,
      CodeNode,
      CodeHighlightNode,
      TableNode,
      TableCellNode,
      TableRowNode,
      AutoLinkNode,
      LinkNode,
      MentionNode,
      ImageNode
    ],
    editorState: value ?? lexicalDefaultValue
  }

  const handleChange = ({ editor }) => {
    const currentContent = JSON.stringify(editor.getEditorState())
    if (currentContent !== value) {
      onChange(name, currentContent, editor)
    }
  }

  return (
    <div id={name} className={clsx(styles['lexical-body'], className)}>
      <LexicalComposer initialConfig={editorConfig}>
        <MyEditor
          mentionsLists={mentionsLists}
          readOnly={readOnly}
          handleChange={handleChange}
          value={value}
          onEditorReady={onEditorReady}
        />
      </LexicalComposer>
    </div>
  )
}

const MyEditor = ({ mentionsLists, readOnly, handleChange, value, onEditorReady }) => {
  const [editor] = useLexicalComposerContext()

  useEffect(() => {
    editor.setEditable(!readOnly)
    if (value) {
      const parsedValue = typeof value === 'string' ? JSON.parse(value) : value
      const currentState = editor.getEditorState().toJSON()
      if (JSON.stringify(currentState) !== JSON.stringify(parsedValue)) {
        editor.setEditorState(editor.parseEditorState(parsedValue))
      }
    }
  }, [value, editor, readOnly])

  return (
    <div className={styles['editor-container']}>
      {editor.isEditable() && <ToolbarPlugin />}
      <div className={styles['editor-inner']}>
        <RichTextPlugin
          contentEditable={<ContentEditable className={styles['editor-input']} />}
          placeholder={<Placeholder />}
          ErrorBoundary={LexicalErrorBoundary}
        />
        {mentionsLists && <MentionsPlugin lists={mentionsLists} />}
        <HistoryPlugin />
        <OnChangePlugin onChange={(editorState, editor) => handleChange({ editor, editorState })} />
        <CodeHighlightPlugin />
        <ListPlugin />
        <LinkPlugin />
        <AutoLinkPlugin />
        <ListMaxIndentLevelPlugin maxDepth={7} />
        <MarkdownShortcutPlugin transformers={TRANSFORMERS} />
        <ImagesPlugin captionsEnabled={true} />
        <EditorRefPlugin onEditorReady={onEditorReady} />
      </div>
    </div>
  )
}
