import React, { useContext } from 'react'
import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import { PageElementContainer } from '../../../Page/PageElementContainer/PageElementContainer'
import { useOnBoardingContextProvider } from '../../../../../public/ContextProviders/OnBoardingContextProvider'
import {
  filterChildFormDefinitions,
  filterChildFormSubmissions
} from '../../../../WorkflowBuilder/WorkflowNode/ApproverStatus/OverviewStatus'
import { DateColumnTemplate } from '../../../../UI/Dashboards/UI/DateColumnTemplate/DateColumnTemplate'
import { useRouter } from 'next/router'

import UserProfileContext from '../../../../../public/UserProfileContext/UserProfileContext'

import styles from './LineItemsDashboard.module.css'

export const LineItemsDashboard = ({ formSubmission, handleFormApprovalPageChange }) => {
  const router = useRouter()
  const userProfile = useContext(UserProfileContext)
  const { childFormSubmissions, childForms, topOfPageRef } = useOnBoardingContextProvider()
  let filteredChildFormSubmissions = filterChildFormSubmissions(userProfile, childFormSubmissions)
  let filteredChildFormDefinitions = filterChildFormDefinitions(userProfile, filteredChildFormSubmissions, childForms)

  // Add the child form definitions to each child form submission. - Alex
  filteredChildFormSubmissions.forEach((childFormSubmission, index) => {
    childFormSubmission.childFormDefinition = filteredChildFormDefinitions[index]
  })

  // Filter out form submissions where the formSubmissionId matches the formSubmissionId in the prop
  if (formSubmission && formSubmission.formSubmissionId) {
    filteredChildFormSubmissions = filteredChildFormSubmissions.filter(
      (submission) => submission?.formSubmission?.formSubmissionId !== formSubmission?.formSubmissionId
    )
  }

  const handleFormIdClick = (rowData) => {
    if (!rowData) return

    // Get the current pathname
    const currentPath = router.asPath

    // Split the path into segments
    const pathSegments = currentPath.split('/')

    // Replace the last segment with the new value
    pathSegments[pathSegments.length - 1] = rowData?.formSubmission?.formSubmissionId

    // Reconstruct the new URL
    const newPath = pathSegments.join('/')

    // Get the base URL (host)
    const baseUrl = window.location.origin

    // Construct the full URL
    const fullUrl = `${baseUrl}/form-builder-studio${newPath}`

    // Open the modified URL in a new tab
    window.open(fullUrl, '_blank')
  }

  const formIdTemplate = (rowData) => {
    return (
      <span style={{ color: 'blue', textDecoration: 'underline', cursor: 'pointer' }} onClick={() => handleFormIdClick(rowData)}>
        {rowData?.formSubmission?.formSubmissionId}
      </span>
    )
  }

  return (
    <PageElementContainer>
      <div className={styles.header}>Line Items</div>
      <DataTable value={filteredChildFormSubmissions} className="p-datatable-line-items">
        <Column field="formSubmission.formSubmissionId" header="Line ID" headerStyle={{ width: '2%' }} body={(e) => formIdTemplate(e)} />
        <Column field="formSubmission.formDefinition.name" header="Name" headerStyle={{ width: '10%' }} />
        <Column field="formSubmission.stageTitle" header="Stage" headerStyle={{ width: '10%' }} />
        <Column field="formSubmission.statusName" header="Status" headerStyle={{ width: '10%' }} />
        <Column field="formSubmission.formDefinition.updatedByDisplayName" header="Updated By" headerStyle={{ width: '11%' }} sortable />
        <Column
          field="updatedAtUtc"
          body={(rowData) => <DateColumnTemplate date={rowData?.formSubmission?.formDefinition.dateUpdated} useTimestampFormat />}
          header="Updated On"
          headerStyle={{ width: '10%' }}
          sortable
        />
      </DataTable>
    </PageElementContainer>
  )
}
