import { Dropdown } from 'primereact/dropdown'
import { forwardRef, useState, memo } from 'react'
import style from './SelectInput.module.css'
import clsx from 'clsx'
import { MultiSelect } from 'primereact/multiselect'

function SelectInput(
  {
    id,
    label,
    className,
    parentClassName = '',
    required,
    orientation = 'column',
    customWidth,
    error,
    options,
    optionLabel,
    optionValue,
    evenLayout = false,
    multiple = false,
    loading = false,
    theme = 'default',
    variant,
    height = 'auto',
    ...props
  },
  ref
) {
  const columnOrientation = {
    display: 'flex',
    flexDirection: orientation,
    rowGap: '10px',
    width: '100%',
    color: 'var(--primary-bg-blackPearl)'
  }
  const rowOrientation = {
    display: 'flex',
    flexDirection: orientation,
    columnGap: '1.5rem',
    width: '100%',
    color: 'var(--primary-bg-blackPearl)'
  }

  return (
    <div className={parentClassName} style={orientation === 'column' ? (evenLayout ? customWidth : columnOrientation) : rowOrientation}>
      {label && (
        <label htmlFor={id} className={theme === 'metronic' ? style.metronicLabel : style.label}>
          {label}
          {required ? <span className={style.starSymbol}> *</span> : null}
        </label>
      )}
      <div className={style.inputCol}>
        {multiple ? (
          <MultiSelect
            ref={ref}
            id={id}
            className={clsx(
              className,
              theme === 'metronic' ? 'custom-lead' : '',
              theme === 'metronic' ? style.metronicSelect : style.select,
              variant === 'underline' && style.selectUnderline
            )}
            options={options}
            optionLabel={optionLabel}
            optionValue={optionValue}
            loading={loading}
            panelClassName={theme === 'metronic' ? 'custom-lead' : ''}
            {...props}
          />
        ) : (
          <Dropdown
            ref={ref}
            id={id}
            className={clsx(
              className,
              theme === 'metronic' ? 'custom-lead' : '',
              theme === 'metronic' ? style.metronicSelect : style.select,
              variant === 'underline' && style.selectUnderline
            )}
            options={options}
            optionLabel={optionLabel}
            optionValue={optionValue}
            loading={loading}
            panelClassName={theme === 'metronic' ? 'custom-lead' : ''}
            {...props}
            style={{ height: height }}
          />
        )}
        {error !== '' && <small className={style.starSymbol}>{error}</small>}
      </div>
    </div>
  )
}

export default forwardRef(SelectInput)
