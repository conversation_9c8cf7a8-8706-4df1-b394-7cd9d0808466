import Button from '../Button/Button'
import TextInput from '../Input/TextInput/TextInput'
import Modal from '../Modal/Modal'
import { $generateHtmlFromNodes } from '@lexical/html'
import { formBuilderApiRequest } from '../../../src/msalConfig'
import { getAccessTokenForScopeSilent } from '../../../src/GetAccessTokenForScopeSilent'
import { useRef, useState } from 'react'
import { useMsal } from '@azure/msal-react'
import { useHandleTimelineEvent } from '../../../hooks/LeadGeneration/useHandleTimelineEvent'
import Editor from '../../LexicalEditor/LexicalEditor'

const api = process.env.NEXT_PUBLIC_FORM_BUILDER_API

export const MailComponent = ({ visible, emailTo, onHide, toastRef, leadId, leadName, stagesJson, fetchLeadDetails }) => {
  const { accounts } = useMsal()
  const account = accounts[0] ?? {}
  const email = account?.username ?? ''
  const displayName = account?.name ?? ''
  const { handleTimelineEvent } = useHandleTimelineEvent()

  const [emailData, setEmailData] = useState({
    recipient: emailTo,
    subject: '',
    body: ''
  })
  const [loading, setLoading] = useState(false)

  const [lexicalEditor, setLexicalEditor] = useState(null)

  const convertToHtml = () => {
    let htmlString = ''
    lexicalEditor.read(() => {
      htmlString = $generateHtmlFromNodes(lexicalEditor)
    })
    return htmlString
  }

  const handleSend = async () => {
    try {
      setLoading(true)
      const formattedBody = emailData.body.replace(/\n/g, '<br>')

      let htmlContent = convertToHtml()
      htmlContent = htmlContent.replace('[LeadName]', leadName)
      htmlContent = htmlContent.replace('[UserDisplayName]', emailData.recipient)

      const accessToken = await getAccessTokenForScopeSilent(formBuilderApiRequest)
      const response = await fetch(`${api}Email/Send`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`
        },
        body: JSON.stringify({
          leadId: leadId,
          toAddresses: [emailData.recipient],
          subject: emailData?.subject ?? '',
          bodyHtml: htmlContent, // Send the complete HTML email
          body: emailData.body
        })
      })

      if (!response.ok) {
        throw new Error('Network response was not ok')
      }

      // Create timeline event for email sent
      await handleTimelineEvent(
        'Email sent',
        leadId,
        stagesJson,
        displayName,
        `<b>From:</b> ${email}<br/>
  <b>To:</b> ${emailData.recipient}<br/>
  <b>Subject:</b> ${emailData.subject}<br/>
  <b>Message:</b> ${formattedBody}`,
        fetchLeadDetails
      )

      if (toastRef && toastRef.current) {
        toastRef.current.show({
          severity: 'success',
          summary: 'Success',
          detail: 'Email sent successfully',
          life: 3000
        })
      }

      onHide()
    } catch (error) {
      console.error('Error sending email:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <>
      <Modal header="Email" visible={visible} onHide={onHide} theme="metronic" style={{ backgroundColor: '#fff', width: '40vw' }}>
        <div className="my-3">
          <div className="flex gap-4 ">
            <TextInput value={email} disabled label="From" theme="metronic" />
            <TextInput
              disabled
              value={emailData.recipient}
              onChange={(e) => setEmailData({ ...emailData, recipient: e.target.value })}
              label="Recipient"
              theme="metronic"
            />
          </div>
          <TextInput
            label="Subject"
            value={emailData.subject}
            onChange={(e) => setEmailData({ ...emailData, subject: e.target.value })}
            theme="metronic"
            parentClassName="my-4"
            required
          />
          <div className="relative w-full flex align-items-end">
            <Editor
              id="email-body-editor"
              name="emailBody"
              onChange={(value) => setEmailData({ ...emailData, body: value })}
              className="w-full h-64"
              readOnly={false}
              onEditorReady={(editor) => setLexicalEditor(editor)}
            />
          </div>
          <div className="flex justify-content-end gap-4 mt-5">
            <Button label="Cancel" theme="metronic" variant="outline" width="120px" onClick={onHide} />
            <Button label="Send" theme="metronic" variant="fill" width="120px" loading={loading} onClick={handleSend} />
          </div>
        </div>
      </Modal>
    </>
  )
}
