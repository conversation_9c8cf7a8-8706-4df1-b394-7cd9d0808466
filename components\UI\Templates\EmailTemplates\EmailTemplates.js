import { useState, useEffect } from 'react'
import clsx from 'clsx'
import { useAccount, useMsal } from '@azure/msal-react'
import { formBuilderApiRequest } from '../../../../src/msalConfig'
import { getAccessTokenForScopeSilent } from '../../../../src/GetAccessTokenForScopeSilent'
import Button from '../../../../components/UI/Button/Button'
import SelectInput from '../../Input/SelectInput/SelectInput'
import TextInput from '../../Input/TextInput/TextInput'
import TextareaInput from '../../Input/TextareaInput/TextareaInput'
import Editor from '../../../../components/LexicalEditor/LexicalEditor'
import styles from '../../../../pages/LeadGeneration/index.module.css'
import tStyle from '../../../../pages/template/template.module.css'
import { $generateHtmlFromNodes, $generateNodesFromDOM } from '@lexical/html'
import { $getRoot, $insertNodes } from 'lexical'
import emailTemplateStyles from './EmailTemplates.module.css'

const api = process.env.NEXT_PUBLIC_FORM_BUILDER_API

const contentTypeOptions = [
  { label: 'Lexical', value: 0 },
  { label: 'HTML', value: 1 }
]

// Default template object
const defaultNewTemplate = {
  id: 'new',
  name: '',
  subject: '',
  content: '',
  contentHtml: '',
  type: 0
}

function TemplateBuilderTab() {
  const { accounts } = useMsal()
  const account = useAccount(accounts[0] ?? {})
  const [existingTemplates, setExistingTemplates] = useState([])
  const [templates, setTemplates] = useState({})
  const [selectedTemplate, setSelectedTemplate] = useState(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (account) {
      loadExistingTemplates()
    }
  }, [account])

  const [templateLoading, setTemplateLoading] = useState(false)
  const loadExistingTemplates = async () => {
    try {
      setTemplateLoading(true)
      const accessToken = await getAccessTokenForScopeSilent(formBuilderApiRequest)
      const response = await fetch(`${api}EmailTemplates`, {
        headers: { Authorization: `Bearer ${accessToken}` }
      })

      if (response.ok) {
        const data = await response.json()

        const templatesData = {
          new: { ...defaultNewTemplate },
          ...data.reduce((acc, t) => {
            acc[t.id.toString()] = t
            return acc
          }, {})
        }

        setTemplates(templatesData)

        setExistingTemplates(
          Object.values(templatesData).map((template) => ({
            label: template.id === 'new' ? 'Create New Template' : template.name,
            value: template.id.toString()
          }))
        )
      }
    } catch (err) {
      setExistingTemplates([{ label: 'Create New Template', value: 'new' }])
      setTemplates({ new: { ...defaultNewTemplate } })
    } finally {
      setTemplateLoading(false)
    }
  }

  const onTemplateUpdate = ({ guid, name, value }) => {
    setTemplates((prevTemplates) => ({
      ...prevTemplates,
      [guid]: {
        ...prevTemplates[guid],
        [name]: value
      }
    }))
  }

  const upsertTemplateAsync = async () => {
    if (!selectedTemplate || !templates[selectedTemplate]) {
      console.error('No template selected for update')
      return
    }

    try {
      setLoading(true)

      const currentTemplate = templates[selectedTemplate]
      const accessToken = await getAccessTokenForScopeSilent(formBuilderApiRequest)
      const isNewTemplate = selectedTemplate === 'new'

      if (isNewTemplate) {
        currentTemplate.id = null
      }

      const response = await fetch(`${api}EmailTemplates`, {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(currentTemplate)
      })

      if (!response.ok) {
        throw new Error(`Failed to save template: ${response.status} ${response.statusText}`)
      }

      const result = await response.json()

      if (isNewTemplate && result) {
        const newTemplateId = result.id.toString()

        setTemplates((prevTemplates) => ({
          ...prevTemplates,
          [newTemplateId]: result,
          new: { ...defaultNewTemplate } // Reset using default object
        }))

        setExistingTemplates((prevOptions) => [
          prevOptions[0],
          {
            label: result.name,
            value: newTemplateId
          },
          ...prevOptions.slice(1)
        ])

        setSelectedTemplate(newTemplateId)
      }
    } catch (err) {
      console.error(`Error saving template:`, err)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div>
      <div style={{ marginTop: '1rem' }}>
        <SelectInput
          loading={templateLoading}
          label="Select Template"
          value={selectedTemplate}
          options={existingTemplates}
          onChange={(e) => {
            console.log('Selected template value:', e.target.value)
            setSelectedTemplate(e.target.value)
          }}
          theme="metronic"
        />
      </div>

      <div className="flex" style={{ gap: '1.875rem' }}>
        <div className={clsx('p-0', styles.gridCard)} style={{ width: '50%' }}>
          <div className={styles.formTitle}>Template Information</div>
          <EmailTemplate
            template={templates[selectedTemplate]}
            onTemplateUpdate={onTemplateUpdate}
            disabled={!selectedTemplate}
            onClick={upsertTemplateAsync}
            loading={loading}
          />
        </div>
        <div className={clsx('p-0', styles.gridCard)} style={{ width: '50%' }}>
          <div className={styles.formTitle}>Preview</div>
          <EmailTemplatePreview content={templates[selectedTemplate]?.content || ''} type={templates[selectedTemplate]?.type || 0} />
        </div>
      </div>
    </div>
  )
}

const EmailTemplate = ({ template, onTemplateUpdate, disabled, onClick, loading }) => {
  const [lexicalEditor, setLexicalEditor] = useState(null)

  useEffect(() => {
    if (lexicalEditor && template?.type === 0) {
      lexicalEditor.update(() => {
        const parser = new DOMParser()
        const dom = parser.parseFromString(template.content, 'text/html')

        const nodes = $generateNodesFromDOM(lexicalEditor, dom)

        $getRoot().clear()
        $getRoot().select()
        $insertNodes(nodes)
      })
    }
  }, [lexicalEditor, template?.content, template?.id])

  const handleLexicalChange = (value) => {
    if (!lexicalEditor) return
    let htmlString = ''
    lexicalEditor.read(() => {
      htmlString = $generateHtmlFromNodes(lexicalEditor)
    })
    onTemplateUpdate({ guid: template?.id, name: 'content', value: htmlString })
  }

  return (
    <div className={emailTemplateStyles.emailTemplateContainer}>
      <TextInput
        label="Template Name"
        value={template?.name ?? ''}
        onChange={(e) => onTemplateUpdate({ guid: template?.id, name: 'name', value: e.target.value })}
        theme="metronic"
        required={true}
      />
      <SelectInput
        label="Content Type"
        value={template?.type}
        options={contentTypeOptions}
        onChange={(e) => onTemplateUpdate({ guid: template?.id, name: 'type', value: e.target.value })}
        theme="metronic"
      />
      <TextInput
        label="Email Subject"
        value={template?.subject ?? ''}
        onChange={(e) => onTemplateUpdate({ guid: template?.id, name: 'subject', value: e.target.value })}
        theme="metronic"
        placeholder="Enter email subject"
        style={{ width: '100%', marginBottom: '20px' }}
        required={true}
      />
      {template?.type === 1 ? (
        <TextareaInput
          value={template?.content ?? ''}
          onChange={(e) => onTemplateUpdate({ guid: template?.id, name: 'content', value: e.target.value })}
          theme="metronic"
        />
      ) : (
        <Editor
          name="content"
          onEditorReady={(editor) => setLexicalEditor(editor)}
          onChange={(name, currentContent, value) => handleLexicalChange(value)}
        />
      )}
      <Button
        label={template?.id !== 'new' ? 'Update Template' : 'Create Template'}
        onClick={onClick}
        disabled={loading}
        theme="metronic"
        variant="fill"
        loading={loading}
        style={{
          alignSelf: 'flex-end',
          marginTop: '1rem'
        }}
      />
    </div>
  )
}

const EmailTemplatePreview = ({ content }) => {
  return (
    <div className={tStyle.previewBox} style={{ padding: '20px' }}>
      <div className={tStyle.previewContent}>
        <div className="email-preview" dangerouslySetInnerHTML={{ __html: content ?? '' }}></div>
      </div>
    </div>
  )
}

export default TemplateBuilderTab
