.emailTemplateContainer {
  display: flex;
  padding: 1rem;
  flex-direction: column;
  gap: 1rem;
}

.emailTemplateContainer.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.container {
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.containerDisabled {
  opacity: 0.5;
  pointer-events: none;
}

.row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.flex1 {
  flex: 1;
}

.fullWidth {
  width: 100%;
}

.mb20 {
  margin-bottom: 20px;
}

.column {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.textarea {
  font-family: monospace;
  font-size: 13px;
  resize: vertical;
}

.alignEnd {
  align-self: flex-end;
}
