import { useContext, useState } from 'react'
import axios from 'axios'
import { msalInstance, ToastContext } from '../pages/_app'
import { formBuilderApiRequest } from '../src/msalConfig'

let baseUrl = process.env.NEXT_PUBLIC_FORM_BUILDER_API

export const useApi = () => {
  const [response, setResponse] = useState(undefined)
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)
  const [validationErrors, setValidationErrors] = useState({})
  const toast = useContext(ToastContext)

  const callApi = async (params) => {
    if (params.baseUrl) {
      baseUrl = params.baseUrl ?? process.env.NEXT_PUBLIC_FORM_BUILDER_API
    }

    if (loading) {
      return
    }

    const account = await msalInstance.getActiveAccount()

    let headers = {
      // "Content-Type": "application/json",
    }

    if (account) {
      const response = await msalInstance.acquireTokenSilent({
        ...formBuilderApiRequest,
        account: account
      })

      headers.Authorization = `Bearer ${response.accessToken}`
    }

    try {
      setLoading({
        [params.url]: true
      })
      const result = await axios.request({
        ...params,
        url: `${baseUrl}${params.url}`,
        headers
      })
      console.log('result:', result)
      const { data } = result
      if (result.statusCode == 403) {
        toast.current.show({
          severity: 'warn',
          summary: 'Your Already have a session open in another device, please close it to continue',
          detail: 'Check your network connection.',
          life: 5000
        })
        return
      }
      if (result.statusCode == 401) {
        toast.current.show({
          severity: 'warn',
          summary: 'Session closed ',
          detail: 'Login again to continue.',
          life: 5000
        })
        return msalInstance.loginPopup()
      }
      if (data.statusCode == 400) {
        if (data.value.errors) {
          assignValidationErrors(data.value.errors)
        } else if (data.value) {
          setValidationErrors({})
          setResponse(result.data)
          return result
        }
      } else {
        setValidationErrors({})
        setResponse(result.data)
        return result
      }
    } catch (error) {
      setResponse({})
      setError(error)
    } finally {
      setLoading(false)
    }
  }

  const callApiFetch = async (url, fetchParams) => {
    if (loading) {
      return
    }

    try {
      setLoading(true)
      const response = await fetch(url, fetchParams)
      const json = await response.json()

      if (response.statusCode == 401) {
        msalInstance.loginPopup()
      }

      if (response.status == 400) {
        if (json.value.errors) {
          assignValidationErrors(json.value.errors)
        }
      } else {
        setValidationErrors({})
        setResponse(json)
        return json
      }
    } catch (error) {
      setResponse({})
      setError(error)
    } finally {
      setLoading(false)
    }
  }

  const callApiFetchWithThrow = async (url, fetchParams) => {
    if (loading) {
      return
    }

    try {
      setLoading(true)
      const response = await fetch(url, fetchParams)
      const json = await response.json()

      if (data.statusCode == 401) {
        msalInstance.loginPopup()
      }

      if (response.status === 200) {
        setValidationErrors({})
        setResponse(json)
        return json
      } else {
        if (json.errors) {
          assignValidationErrors(json.errors)
        }
        throw new Error('Error fetching data')
      }
    } catch (error) {
      setResponse({})
      setError(error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const assignValidationErrors = (responseErrors) => {
    const errors = []
    responseErrors.forEach((element) => {
      errors[element.propertyName] = [element.errorMessage]
    })
    setValidationErrors(errors)
  }

  return {
    response,
    error,
    loading,
    validationErrors,
    callApi,
    callApiFetch,
    setLoading,
    callApiFetchWithThrow
  }
}
