import { useEffect, useRef, useState } from 'react'
import { PageContainer } from '../../../../components/UI/Page/PageContainer/PageContainer'
import style from '../../../../pages/LMS/index.module.css'
import { useApi } from '../../../../hooks/useApi'
import { ConditionalDisplay } from '../../../../components/UI/ConditionalDisplay/ConditionalDisplay'
import PdfViewer from '../../../../components/UI/Pdf/PdfViewer'
import { LoadingScreen } from '../../../../components/UI/LoadingScreen/LoadingScreen'
import VideoPlayer from '../../../../components/Video/VideoPlayer'
import Button from '../../../../components/UI/Button/Button'
import { useRouter } from 'next/router'
import BreadCrumbs from '../../../../components/UI/BreadCrumbs/BreadCrumbs'
import clsx from 'clsx'

export default function CourseDetail({ courseId, chapterId, courseTitle }) {
  console.log('coursetitle', courseTitle)
  const [chapter, setChapter] = useState(null)
  const { callApi, loading } = useApi()
  const videoRef = useRef(null)
  const pdfRef = useRef(null)
  const sectionRef = useRef(null)
  const router = useRouter()
  const fetchChapter = async () => {
    try {
      const response = await callApi({
        method: 'GET',
        url: `Courses/GetChapter/${chapterId}`
      })
      if (response?.data) {
        setChapter(response.data)
      }
    } catch (error) {
      console.error('Error fetching chapter:', error)
    }
  }

  useEffect(() => {
    if (chapterId) {
      fetchChapter()
    }
  }, [chapterId])

  useEffect(() => {
    if ((chapter?.fileType === 'video/mp4' && chapter?.fileUrl) || (chapter?.fileType === 'application/pdf' && chapter?.fileUrl)) {
      // Try multiple times with increasing delays
      const timers = [
        setTimeout(() => {
          chapter?.fileType === 'video/mp4' ? scrollToVideo() : scrollToPdf()
        }, 500),
        setTimeout(() => {
          chapter?.fileType === 'video/mp4' ? scrollToVideo() : scrollToPdf()
        }, 1500),
        setTimeout(() => {
          chapter?.fileType === 'video/mp4' ? scrollToVideo() : scrollToPdf()
        }, 3000)
      ]

      return () => timers.forEach((timer) => clearTimeout(timer))
    }
  }, [chapter])

  const scrollToPdf = (ref) => {
    requestAnimationFrame(() => {
      setTimeout(() => {
        const targetElement = ref?.current || document.getElementById('pdf-section')

        if (targetElement) {
          try {
            targetElement.scrollIntoView({
              behavior: 'smooth',
              block: 'center',
              inline: 'nearest'
            })
          } catch (error) {
            const viewportHeight = window.innerHeight
            const elementRect = targetElement.getBoundingClientRect()
            const scrollTo = window.pageYOffset + elementRect.top - (viewportHeight - elementRect.height) / 2

            window.scrollTo({
              top: Math.max(0, scrollTo),
              behavior: 'smooth'
            })
          }
        }
      }, 100)
    })
  }

  const scrollToVideo = () => {
    // Use requestAnimationFrame to ensure DOM is painted
    requestAnimationFrame(() => {
      setTimeout(() => {
        let targetElement = null

        // Try to use the refs first
        if (videoRef.current) {
          targetElement = videoRef.current
        } else if (sectionRef.current) {
          targetElement = sectionRef.current
        } else {
          // Fallback to finding the video section by ID
          targetElement = document.getElementById('video-section')
        }

        if (targetElement) {
          const viewportHeight = window.innerHeight
          const elementRect = targetElement.getBoundingClientRect()

          // Calculate position to center element in viewport
          const scrollTo = window.pageYOffset + elementRect.top - (viewportHeight - elementRect.height) / 2

          // Try scrollIntoView first as it's more reliable
          try {
            targetElement.scrollIntoView({
              behavior: 'smooth',
              block: 'center',
              inline: 'nearest'
            })
          } catch (error) {
            // Fallback to window.scrollTo
            window.scrollTo({
              top: Math.max(0, scrollTo),
              behavior: 'smooth'
            })
          }
        } else {
          console.log('No target element found for scrolling')
        }
      }, 100) // Small delay to ensure DOM is updated
    })
  }

  return (
    <PageContainer>
      <div className="flex flex-column gap-5">
        <div className="flex  align-items-center gap-2 mt-1">
          <BreadCrumbs
            title="My Course"
            breadcrumbItems={[{ label: 'My Courses' }, { label: courseTitle }, { label: chapter?.chapterTitle }]}
            theme="metronic"
          />
        </div>
        <div className={style.pageCard}>
          <h2 className={clsx('mt-0 text-center', style.formTitle)}>{chapter?.chapterTitle}</h2>
          {console.log('chapter', chapter)}
          <ConditionalDisplay condition={loading}>
            <LoadingScreen />
          </ConditionalDisplay>
          <ConditionalDisplay condition={chapter?.fileType === 'video/mp4'}>
            <section ref={sectionRef} className=" flex flex-column align-items-center m-8" id="video-section">
              <VideoPlayer
                ref={videoRef}
                src={chapter?.fileUrl}
                width="100%"
                height="600px"
                autoplay={true}
                muted={true}
                onLoad={scrollToVideo}
                onError={(error) => console.error('Video error:', error)}
                onVideoComplete={() => setOnVideoComplete(true)}
              />
              <div className="mt-5 mb-5 flex gap-3 justify-content-end w-full">
                <Button label="Back" theme="metronic" width="150px" variant="outline" onClick={() => router.back()}></Button>
              </div>
            </section>
          </ConditionalDisplay>
          <ConditionalDisplay condition={chapter?.fileType === 'application/pdf'}>
            <div className="flex flex-column align-items-center w-full" ref={pdfRef} id="pdf-section">
              <PdfViewer fileUrl={chapter?.fileUrl} loading={loading} Instructor={true} />
              <div className="my-5">
                <Button label="Back" theme="metronic" width="150px" variant="outline" onClick={() => router.back()} />
              </div>
            </div>
          </ConditionalDisplay>
        </div>
      </div>
    </PageContainer>
  )
}
export async function getServerSideProps(context) {
  const { courseId, id } = context.params
  const { courseTitle } = context.query
  return {
    props: {
      courseId,
      chapterId: id,
      courseTitle: courseTitle
    }
  }
}
