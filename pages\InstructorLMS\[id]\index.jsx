import Image from 'next/image'
import { PageContainer } from '../../../components/UI/Page/PageContainer/PageContainer'
import BreadCrumbs from '../../../components/UI/BreadCrumbs/BreadCrumbs'
import Backarrow from '../../../svg/metronic/back_metronic.svg'
import Button from '../../../components/UI/Button/Button'
import style from '../../../pages/LMS/index.module.css'
import { useContext, useEffect, useRef, useState } from 'react'
import { ConditionalDisplay } from '../../../components/UI/ConditionalDisplay/ConditionalDisplay'
import Modal from '../../../components/UI/Modal/Modal'
import clsx from 'clsx'
import Delete from '../../../svg/metronic/delete.svg'
import Edit from '../../../svg/metronic/pencil_line.svg'
import PdfThumbnail from '../../../svg/metronic/LMS_pdf.svg'
import VideoThumbnail from '../../../svg/metronic/video_icon.svg'
import Assessment from '../../../public/svg/LMS/assessment.svg'
import { useCourse } from '../../../hooks/LMS/Course/useCourse'
import UserProfileContext from '../../../public/UserProfileContext/UserProfileContext'
import { Tab } from '../../../components/UI/Tabs/Tabs'
import { useChapterTabs } from '../../../hooks/LMS/Course/useChapterTabs'
import CreateChapterModal from '../../../components/LMS/Modals/CreateChapterModal'
import CreateModuleModal from '../../../components/LMS/Modals/CreateModuleModal'
import useDeleteChapter from '../../../hooks/LMS/Course/useDeleteChapter'
import Assignment from '../../../components/LMS/Assignment'
import ManageStudent from '../../../components/LMS/ManageStudent'
import ManageGrades from '../../../components/LMS/ManageGrades'
import Settings from '../../../components/LMS/Settings'
import { useAssessment } from '../../../hooks/LMS/Course/useAssessment'
import { useCreateCourse } from '../../../hooks/LMS/Course/useCreateCourse'
import { useCourseType } from '../../../hooks/LMS/Course/useCourseType'
import { useRouter } from 'next/router'
import { Toast } from 'primereact/toast'
import ManageStudentModal from '../../../components/LMS/Modals/ManageStudentModal'
import Reject from '../../../svg/metronic/reject.svg'
import Success from '../../../svg/metronic/success.svg'
import Close from '../../../svg/metronic/close_toast.svg'
import { EmptyDataTemplate } from '../../../components/LMS/LMSTableComponents'
import { CertificateBuilder } from '../../../components/LeadGeneration/LeadGenerationTabs/CertificateBuilder'
import { useApi } from '../../../hooks/useApi'
import { msalInstance } from '../../_app'
import { formBuilderApiRequest } from '../../../src/msalConfig'
import TextInput from '../../../components/UI/Input/TextInput/TextInput'
import TextareaInput from '../../../components/UI/Input/TextareaInput/TextareaInput'
import SelectInput from '../../../components/UI/Input/SelectInput/SelectInput'
import CreateAssessment from '../../../components/LMS/CreateAssessment'
import { set } from 'lodash'

const ContentList = ({
  title,
  items,
  setItems,
  author,
  courseTitle,
  fetchAvailableCourseById,
  toastNew,
  setShowCreateModuleModal,
  setType
}) => {
  const [dragIndex, setDragIndex] = useState(null)
  const toast = useRef(null)
  const router = useRouter()
  const { handleDeleteChapter, loading } = useDeleteChapter(toast)

  const handleDragStart = (index) => setDragIndex(index)
  const handleDrop = (dropIndex) => {
    if (dragIndex === null || dragIndex === dropIndex) return
    const updated = [...items]
    const dragged = updated.splice(dragIndex, 1)[0]
    updated.splice(dropIndex, 0, dragged)
    setItems(updated.map((item, i) => ({ ...item, order: i + 1 })))
    setDragIndex(null)
  }
  const handleDeleteCallback = () => {
    toastNew.current?.show({
      severity: 'success',
      summary: 'Success',
      detail: `Module Deleted Successfully!`
    })
    fetchAvailableCourseById()
  }
  return (
    <div className={style.pageCard}>
      <div className={style.prospecTable}>
        <span className="text-xl font-bold">{title}</span>
      </div>
      <div className="m-4">
        {items.map((data, index) => (
          <div
            className="flex align-items-center"
            key={`${title}-${index}`}
            draggable
            onDragStart={() => handleDragStart(index)}
            onDragOver={(e) => e.preventDefault()}
            onDrop={() => handleDrop(index)}
            style={{ cursor: 'grab' }}
          >
            <div>{index + 1}</div>
            <div className={clsx('mx-5 my-3 cursor-pointer', style.pageCard)}>
              <div className="flex justify-content-between align-items-center p-5">
                <div className="flex align-items-center gap-4">
                  <i className={clsx('pi pi-bars', style.bar)}></i>
                  <Image
                    src={data.chapterType ? Assessment : data.fileType === 'application/pdf' ? PdfThumbnail : VideoThumbnail}
                    alt="pdf-thumbnail"
                  />
                  <div className="flex flex-column gap-2">
                    <div className="flex gap-4 align-items-center">
                      <span className={style.courseTitle}>{data?.chapterTitle}</span>
                      {/* <Image
                        src={Edit}
                        alt="edit"
                        className="cursor-pointer"
                        onClick={() => {
                          // Open modal and implement edit logic here
                          //setShowCreateChapterModal(true)
                          // Optionally, set the current chapter/module to edit
                          //setEditingChapter(data)
                          setType(1)
                          setShowCreateModuleModal(true)
                        }}
                      /> */}
                    </div>
                    <span className={clsx('text-sm', style.fontText)}>{data.overview}</span>
                    <span className={style.courseAuthor}>By {author}</span>
                  </div>
                </div>
                <Button
                  label="view"
                  theme="metronic"
                  variant="outline"
                  width="150px"
                  onClick={() => {
                    console.log('data chapter', data)
                    data.chapterType === 2
                      ? router.push({
                          pathname: `/InstructorLMS/${data.courseId}/Assessment`,
                          query: {
                            chapterId: data.id
                          }
                        })
                      : router.push({
                          pathname: `/InstructorLMS/Courses/${data.courseId}/${data.id}`,
                          query: {
                            courseTitle: courseTitle
                          }
                        })
                  }}
                />
              </div>
            </div>
            <Image
              src={Delete}
              alt="delete"
              onClick={() => handleDeleteChapter(data.id, handleDeleteCallback)}
              className="cursor-pointer"
            />
          </div>
        ))}
      </div>
      <ConditionalDisplay condition={items?.length === 0}>
        <EmptyDataTemplate message="No Course Content Added" />
      </ConditionalDisplay>
    </div>
  )
}

export default function NewCourse({ id, courseTitle }) {
  const router = useRouter()
  const toast = useRef(null)
  const [showCreateChapterModal, setShowCreateChapterModal] = useState(false)
  const [showCreateModuleModal, setShowCreateModuleModal] = useState(false)
  const [showManageStudentsModal, setShowManageStudentsModal] = useState(false)

  const { userID: userId } = useContext(UserProfileContext)
  const { fetchAvailableCourseById, loading, course, setCourse } = useCourse(id, userId)
  const { currentTab, setCurrentTab, tabs } = useChapterTabs()
  const { courseTypes, fetchCourseTypes } = useCourseType()

  const [type, setType] = useState(0)

  const { fetchAssessmentWithAnswers, questionAnswers, createAssessment, updateAssessment } = useAssessment()

  const { updateCourse } = useCreateCourse()

  const [HtmlJSON, setHtmlJSON] = useState('')

  const { callApi, callApiFetch, loading: isUpdating } = useApi()

  useEffect(() => {
    if (id) {
      fetchAssessmentWithAnswers(id)
    }
  }, [id])

  useEffect(() => {
    if (questionAnswers?.length > 0) {
      const transformedQuestions = questionAnswers?.map((item) => ({
        id: item.id,
        question: item.question,
        options: item.choices,
        answer: item.choices
          ?.filter((choice) => item?.answers?.includes(choice.id))
          .map((choice) => choice.choiceText)
          .join(',')
      }))
      setQuestions(transformedQuestions)
    }
  }, [questionAnswers])

  useEffect(() => {
    fetchCourseTypes()
  }, [])

  const [questions, setQuestions] = useState([
    {
      id: 1,
      question: '',
      options: [{ choiceText: '' }, { choiceText: '' }, { choiceText: '' }, { choiceText: '' }],
      answer: ''
    }
  ])

  useEffect(() => {
    if (id && userId) {
      fetchAvailableCourseById()
    }
  }, [id, userId])

  useEffect(() => {
    console.log('course', course)
    if (course) {
      setChapterData(course.chapters)
    }
  }, [course])

  const [chapterData, setChapterData] = useState([])
  // const [quizData, setQuizData] = useState([
  //   {
  //     title: 'Quiz 1: WordPress Basics',
  //     overview: 'Test your knowledge of basic WordPress concepts.',
  //     createdUser: 'Jane Smith',
  //   },
  //   {
  //     title: 'Quiz 2: Themes and Plugins',
  //     overview: 'A quiz on using and managing themes and plugins.',
  //     createdUser: 'Jane Smith',
  //   },
  // ])

  console.log('course', chapterData)

  const publishCourse = (status = true) => {
    // Validate: At least one chapter
    if (!chapterData || chapterData.length === 0) {
      toast.current.show({
        severity: 'warn',
        summary: 'Validation Error',
        detail: 'Please add at least one Chapter before publishing the course.',
        icon: <Image src={Reject} alt="error" />,
        closeIcon: <Image src={Close} alt="close" />
      })
      return
    }

    // Validate: At least one questionnaire
    if (!questions || questions.length === 0 || !questions.some((q) => q.question && q.options && q.options.length > 0)) {
      toast.current.show({
        severity: 'warn',
        summary: 'Validation Error',
        detail: 'Please add at least one Questionnaire before publishing the course.',
        icon: <Image src={Reject} alt="error" />,
        closeIcon: <Image src={Close} alt="close" />
      })
      return
    }

    // Validate: If on Settings tab, Availability From and To must be present
    if (!course?.availableFrom || !course?.availableTo) {
      console.log('course', course)
      toast.current.show({
        severity: 'warn',
        summary: 'Validation Error',
        detail: 'Please set both "Availability From" and "Availability To" in the Settings tab before publishing.',
        icon: <Image src={Reject} alt="error" />,
        closeIcon: <Image src={Close} alt="close" />
      })
      return
    }

    const requestBody = {
      courseId: course?.id,
      title: course?.title,
      overview: course?.overview,
      courseTypeId: course?.courseTypeId,
      thumbnailType: course?.thumbnailType,
      thumbnailName: course?.thumbnailName,
      thumbnailSize: course?.thumbnailSize,
      hasPublished: status,
      availableToAll: course?.availableToAll,
      availableToLeadIds: course?.availableToLeadUserProfileIds,
      availableFrom: course?.availableFrom,
      availableTo: course?.availableTo
    }

    updateCourse(requestBody, () => {
      if (status === true) {
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: `Course Published Successfully.`,
          icon: <Image src={Success} alt="success" />,
          closeIcon: <Image src={Close} alt="close" />
        })
        router.back()
      } else {
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: `Settings Update.`,
          icon: <Image src={Success} alt="success" />,
          closeIcon: <Image src={Close} alt="close" />
        })
      }
    })
  }

  const updateChaptersOrder = async (chapters) => {
    const formData = new FormData()
    chapters.forEach((chapter, index) => {
      formData.append(`request[${index}].courseChapterId`, chapter.id)
      formData.append(`request[${index}].order`, chapter.order)
      formData.append(`request[${index}].chapterTitle`, chapter.chapterTitle)
      formData.append(`request[${index}].chapterType`, chapter.chapterType)
      if (chapter.duration) {
        formData.append(`request[${index}].duration`, chapter.duration)
      }
      if (chapter.fileType) {
        formData.append(`request[${index}].fileType`, chapter.fileType)
      }
      if (chapter.fileName) {
        formData.append(`request[${index}].fileName`, chapter.fileName)
      }
      if (chapter.fileSize) {
        formData.append(`request[${index}].fileSize`, chapter.fileSize)
      }
    })
    await callApi({
      method: 'PATCH',
      url: 'Courses/UpdateChapter',
      data: formData
    })
  }

  const saveCertificate = async (htmlJSON, status = true) => {
    const payload = new FormData()
    payload.append('courseId', course?.id)
    payload.append('title', course?.title)
    payload.append('overview', course?.overview)
    payload.append('courseTypeId', course?.courseTypeId)
    payload.append('thumbnailType', course?.thumbnailType)
    payload.append('thumbnailName', course?.thumbnailName)
    payload.append('thumbnailSize', course?.thumbnailSize)
    payload.append('hasPublished', status)
    payload.append('availableToAll', course?.availableToAll)
    if (Array.isArray(course?.availableToLeadUserProfileIds)) {
      course.availableToLeadUserProfileIds.forEach((id, idx) => {
        payload.append(`availableToLeadIds[${idx}]`, id)
      })
    }
    payload.append('availableFrom', course?.availableFrom)
    payload.append('availableTo', course?.availableTo)

    if (htmlJSON) {
      payload.append('CertificateTemplateMetadata', JSON.stringify(htmlJSON))
    }
    try {
      callApi({
        method: 'PATCH',
        url: 'UpdateCourses',
        data: payload
      }).then(() => {
        toast.current.show({
          severity: 'success',
          summary: 'Certificate Saved',
          detail: 'Certificate has been saved successfully.'
        })
      })
    } catch (error) {
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to save certificate.'
      })
    }
  }

  const handleNextStep = async () => {
    switch (currentTab) {
      case 0:
        await updateChaptersOrder(chapterData)
        setCurrentTab(currentTab + 1)
        break
      case 1:
        setCurrentTab(currentTab + 1)
        break
      case 2:
        setCurrentTab(course?.hasPublished ? currentTab + 1 : currentTab + 2)
        break
      case 3:
        setCurrentTab(currentTab + 1)
        break
      case 4:
        await saveCertificate(HtmlJSON)
        break
      case 5:
        publishCourse()
        break
    }
  }

  const validateQuestions = (questions) => {
    return questions.map((q) => {
      const qErrors = {
        question: '',
        options: Array(q.options.length).fill(''),
        answer: ''
      }
      if (!q.question || q.question.trim() === '') {
        qErrors.question = 'Question is required.'
      }
      q.options.forEach((opt, oIdx) => {
        if (!opt.choiceText || opt.choiceText.trim() === '') {
          qErrors.options[oIdx] = 'Option is required.'
        }
      })
      if (!q.answer || q.answer.trim() === '') {
        qErrors.answer = 'Answer is required.'
      }
      return qErrors
    })
  }

  const createOrUpdateAssessment = () => {
    const errors = validateQuestions(questions)
    const hasErrors = errors.some((err) => err.question || err.options.some((o) => o) || err.answer)
    if (hasErrors) {
      toast.current.show({
        severity: 'error',
        summary: 'Validation Error',
        detail: 'At least one question must be filled out correctly.'
      })
      return
    }

    if (questionAnswers?.length > 0) {
      const requestBody = questions.map((question) => ({
        courseAssessmentId: question.options[0].courseAssessmentId,
        courseId: parseInt(id),
        question: question.question,
        updatedBy: userId,
        answers: question.answer.split(','),
        choiceType: 1,
        choices: question.options.map((option) => ({
          choiceText: option.choiceText,
          courseAssessmentChoiceId: option.id
        }))
      }))
      console.log('requestBody', questions)
      updateAssessment(requestBody, () => {
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: `Assessment Updated Successfully.`,
          icon: <Image src={Success} alt="success" />,
          closeIcon: <Image src={Close} alt="close" />
        })
      })
    } else {
      const requestBody = questions.map((question) => ({
        courseId: parseInt(id),
        question: question.question,
        createdBy: userId,
        answers: question.answer.split(','),
        choiceType: 1,
        choices: question.options.map((option) => ({
          choiceText: option.choiceText
        }))
      }))
      createAssessment(requestBody, () => {
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: `Assessment Created Successfully.`,
          icon: <Image src={Success} alt="success" />,
          closeIcon: <Image src={Close} alt="close" />
        })
      })
    }
  }

  const updateCourseSettings = () => {
    publishCourse(false)
  }

  const handleAddAction = () => {
    switch (currentTab) {
      case 0:
        setShowCreateModuleModal(true)
        break
      case 1:
        createOrUpdateAssessment()
        break
      case 2:
        setShowManageStudentsModal(true)
        break
      case 3:
        break
      case 4:
        saveCertificate(HtmlJSON, false)
        break
      case 5:
        updateCourseSettings()
        break
    }
  }

  return (
    <PageContainer theme="metronic">
      <Toast ref={toast} />
      <div className="flex flex-column gap-5">
        <div className="flex gap-4 mt-1">
          <Image src={Backarrow} alt="Back" className="cursor-pointer" onClick={() => router.back()} />
          <BreadCrumbs
            title="Create New Course"
            breadcrumbItems={[{ label: 'Create New Course' }, { label: tabs.find((tab) => tab.value === currentTab)?.title }]}
            theme="metronic"
          />

          <div className="flex gap-3">
            <Button
              label={currentTab === 2 ? 'Manage Students' : currentTab === 4 ? 'Update' : 'Add'}
              variant="outline"
              theme="metronic"
              width={currentTab === 2 ? '200px' : '150px'}
              onClick={handleAddAction}
            />
            <Button
              label={currentTab === 5 ? 'Publish' : 'Next'}
              theme="metronic"
              width="150px"
              onClick={handleNextStep}
              loading={isUpdating}
            />
          </div>
        </div>
        <div className={clsx('flex gap-4', style.tabContainer)} style={{ height: '2.5rem' }}>
          {tabs.map((tab) => {
            if (tab.value === 3 && !course?.hasPublished) {
              return null
            }
            return (
              <Tab
                key={tab.value}
                title={tab.title}
                value={tab.value}
                display={true}
                isActive={currentTab === tab.value}
                handleClick={(e, value) => {
                  setCurrentTab(value)
                }}
                theme="metronic"
              />
            )
          })}
        </div>
        <ConditionalDisplay condition={currentTab === 0}>
          <ContentList
            title="Course Content"
            items={chapterData}
            setItems={setChapterData}
            author={course?.createdUser}
            courseTitle={courseTitle}
            toastNew={toast}
            fetchAvailableCourseById={fetchAvailableCourseById}
            setShowCreateModuleModal={setShowCreateModuleModal}
            setType={setType}
          />
        </ConditionalDisplay>
        <ConditionalDisplay condition={currentTab === 1}>
          <Assignment toast={toast} questions={questions} setQuestions={setQuestions} validateQuestions={validateQuestions} />
        </ConditionalDisplay>
        <ConditionalDisplay condition={currentTab === 2}>
          <ManageStudent courseId={id} />
        </ConditionalDisplay>
        <ConditionalDisplay condition={currentTab === 3}>
          <ManageGrades courseId={id} />
        </ConditionalDisplay>
        <ConditionalDisplay condition={currentTab === 4}>
          <CertificateBuilder metadata={course?.courseCertificateMetadata?.metadata} setHtmlJSON={setHtmlJSON} />
        </ConditionalDisplay>
        <ConditionalDisplay condition={currentTab === 5}>
          <Settings course={course} courseTypes={courseTypes} />
        </ConditionalDisplay>
        {/* <ContentList title="Quizzes" items={quizData} setItems={setQuizData} /> */}
        <Modal
          visible={showCreateModuleModal}
          theme="metronic"
          onHide={() => {
            setType(0)
            setShowCreateModuleModal(false)
          }}
          header={`Add New ${type === 1 ? 'Module' : type === 2 ? 'Assignment' : 'Content'}`}
          style={{ background: '#fff', width: '40vw' }}
        >
          {type === 0 ? (
            <CreateModuleModal
              setType={setType}
              // setShowCreateChapterModal={setShowCreateChapterModal}
              // setShowCreateModuleModal={setShowCreateModuleModal}
            ></CreateModuleModal>
          ) : type === 1 ? (
            <CreateChapterModal
              setShowCreateModuleModal={setShowCreateModuleModal}
              setType={setType}
              order={chapterData.length + 1}
              courseId={id}
              toast={toast}
              fetchAvailableCourseById={fetchAvailableCourseById}
            />
          ) : (
            <CreateAssessment type={type} chapter={chapterData} />
          )}
        </Modal>
        {/* <Modal
          visible={showCreateChapterModal}
          theme="metronic"
          onHide={() => setShowCreateChapterModal(false)}
          header="Add New Module"
          style={{ background: '#fff', width: '40vw' }}
        >
          <CreateChapterModal
            setShowCreateChapterModal={setShowCreateChapterModal}
            order={chapterData.length + 1}
            courseId={id}
            toast={toast}
            fetchAvailableCourseById={fetchAvailableCourseById}
          />
        </Modal> */}
        <Modal
          visible={showManageStudentsModal}
          theme="metronic"
          onHide={() => setShowManageStudentsModal(false)}
          header="Add New Module"
          style={{ background: '#fff', width: '40vw' }}
        >
          <ManageStudentModal
            course={course}
            setCourse={setCourse}
            updateCourse={updateCourse}
            loading={loading}
            setShowManageStudentsModal={setShowManageStudentsModal}
            toast={toast}
          />
        </Modal>
      </div>
    </PageContainer>
  )
}

export async function getServerSideProps(context) {
  const { params } = context
  const { id } = params
  const { courseTitle } = context.query

  return {
    props: {
      id,
      courseTitle: courseTitle
    }
  }
}
