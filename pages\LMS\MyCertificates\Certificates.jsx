import BreadCrumbs from '../../../components/UI/BreadCrumbs/BreadCrumbs'
import Image from 'next/image'
import { PageContainer } from '../../../components/UI/Page/PageContainer/PageContainer'
import style from '../index.module.css'
import Backarrow from '../../../svg/metronic/back_metronic.svg'
import certificate from '../../../svg/metronic/certificate.png'
import download from '../../../svg/metronic/download.svg'
import clsx from 'clsx'
import { useEffect, useRef } from 'react'
import { useRouter } from 'next/router'
import { useApi } from '../../../hooks/useApi'
import timeZone from 'moment-timezone'
import html2pdf from 'html2pdf.js'

export default function Certificates() {
  const previewRef = useRef(null)
  const router = useRouter()
  const { callApi } = useApi()

  useEffect(() => {
    const fetchCertificate = async () => {
      const mappingId = router.query.mappingId
      if (!mappingId) return
      const res = await callApi({
        method: 'GET',
        url: `Leads/LeadCourseMapping/GetCertificate?LeadCourseMappingId=${mappingId}&TimeZone=${
          Intl.DateTimeFormat().resolvedOptions().timeZone
        }`
      })
      if (previewRef.current && res?.data?.courseCertificateMetadata) {
        previewRef.current.innerHTML = res.data?.courseCertificateMetadata?.metadata?.svg ?? 'Sothing Went wrong...'
      }
    }
    fetchCertificate()
  }, [router.query.mappingId])

  const handleDownloadPdf = () => {
    if (!previewRef.current) return
    // Try to get SVG width and height
    const svg = previewRef.current.querySelector('svg')
    let width = 1123
    let height = 794 // Default to A4 landscape in px
    if (svg) {
      // Try width/height attributes first
      const wAttr = svg.getAttribute('width')
      const hAttr = svg.getAttribute('height')
      if (wAttr && hAttr) {
        width = parseInt(wAttr)
        height = parseInt(hAttr)
        console.log('true')
      } else {
        console.log('false')
        // Try viewBox if width/height not set
        const viewBox = svg.getAttribute('viewBox')
        if (viewBox) {
          const parts = viewBox.split(' ')
          if (parts.length === 4) {
            width = parseInt(parts[2])
            height = parseInt(parts[3])
          }
        }
      }
    }
    html2pdf(previewRef.current, {
      margin: 0,
      filename: 'certificate.pdf',
      image: { type: 'png', quality: 1 },
      html2canvas: {
        scale: 3,
        useCORS: true,
        backgroundColor: null
      },
      jsPDF: {
        unit: 'px',
        format: [width, height],
        orientation: width > height ? 'landscape' : 'portrait'
      },
      hotfixes: ['px_scaling']
    })
  }

  return (
    <PageContainer theme="metronic">
      <div className="flex flex-column gap-5">
        <div className="flex align-items-center gap-2 mt-1">
          <Image src={Backarrow} alt="Back" />
          <BreadCrumbs title="My Certificates" breadcrumbItems={[{ label: 'My Certificates' }]} theme="metronic" />
        </div>
        <div className={clsx('p-4 gap-4 flex flex-column', style.pageCard)}>
          <div className="text-right flex align-content-end gap-2">
            <Image src={download} alt="download" style={{ cursor: 'pointer' }} onClick={handleDownloadPdf} />
          </div>
          <div className="text-center">
            <div ref={previewRef} />
          </div>
        </div>
      </div>
    </PageContainer>
  )
}
