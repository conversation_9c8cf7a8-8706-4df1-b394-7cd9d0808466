import { DataTable } from 'primereact/datatable'
import CourseCard from '../../components/LMS/CourseCard'
import ProfileBanner from '../../components/LMS/ProfileBanner'
import { PageContainer } from '../../components/UI/Page/PageContainer/PageContainer'
import { Column } from 'primereact/column'
import styles from './index.module.css'
import emptyCard from '../../svg/metronic/empty_course_card.svg'
import Image from 'next/image'
import Button from '../../components/UI/Button/Button'
import { ConditionalDisplay } from '../../components/UI/ConditionalDisplay/ConditionalDisplay'
import { useContext, useEffect } from 'react'
import UserProfileContext from '../../public/UserProfileContext/UserProfileContext'
import { useAvailableCourses } from '../../hooks/LMS/AvailableCourses/useAvailableCourses'
import { LoadingScreen } from '../../components/UI/LoadingScreen/LoadingScreen'
import { useRouter } from 'next/router'
import { useEnrolledCoursesData } from '../../hooks/LMS/useEnrolledCoursesData'
import {
  ActionTemplate,
  CourseDescriptionTemplate,
  CourseTypeTemplate,
  EmptyDataTemplate,
  TableHeader
} from '../../components/LMS/LMSTableComponents'
import clsx from 'clsx'

export const COURSE_STATUSES = {
  ALL: 1,
  COMPLETED: 2,
  PENDING: 3,
  PENDING_ASSESSMENT: 4
}

export default function LMSDashboard() {
  const router = useRouter()
  const { userID: userId } = useContext(UserProfileContext)

  const ongoingCoursesData = useEnrolledCoursesData(COURSE_STATUSES.PENDING, userId)
  const pendingQuizzesData = useEnrolledCoursesData(COURSE_STATUSES.PENDING_ASSESSMENT, userId)
  const certificatesData = useEnrolledCoursesData(COURSE_STATUSES.COMPLETED, userId)

  const handleCourseAction = (rowData, type) => {
    const route = type === 'certificate' ? `/LMS/MyCertificates/Certificates` : `/LMS/MyCourses/${rowData?.id}`
    router.push(route)
  }

  const {
    courses: availableCourses,
    totalCount: availableCoursesTotalCount,
    loading: availableCoursesLoading,
    fetchCourses: availableCoursesFetchCourses,
    lazyParams: availableCoursesLazyParams
  } = useAvailableCourses(4)

  useEffect(() => {
    availableCoursesFetchCourses(0)
  }, [availableCoursesLazyParams])

  const getTableColumns = (type) => [
    { field: 'icon', header: '', body: CourseTypeTemplate },
    {
      field: 'desc',
      header: '',
      body: (rowData) => <CourseDescriptionTemplate course={rowData?.course} showDescription={true} />
    },
    {
      field: 'resume',
      header: '',
      body: (rowData) => (
        <ActionTemplate
          rowData={rowData}
          actionLabel={type === 'certificate' ? 'View Certificate' : 'Resume'}
          onClick={(data) => handleCourseAction(data, type)}
        />
      )
    }
  ]

  return (
    <PageContainer theme="metronic">
      <ProfileBanner instructor={false} />
      <div className={clsx('my-5 font-bold text-lg', styles.fontText)}>{'Available Courses'}</div>
      <div className="w-full">
        <ConditionalDisplay condition={availableCoursesLoading}>
          <LoadingScreen />
        </ConditionalDisplay>

        <ConditionalDisplay condition={!availableCoursesLoading}>
          {/* Course grid */}
          <div>
            <div className="flex flex-wrap column-gap-3 row-gap-5 justify-content-between">
              {availableCourses?.map((course, index) => {
                console.log('availableCourses', course?.thumbnailUrl)
                return (
                  <CourseCard
                    key={`course-${index}`}
                    courseTitle={course?.title || 'Course Title'}
                    courseDescription={course?.createdUser || 'User'}
                    assignedDate={course?.overview || `This is a ${course?.title} course`}
                    buttonAction={() => router.push(`/LMS/AvailableCourses/${course?.id}`)}
                    thumbnailUrl={course?.thumbnailUrl}
                  />
                )
              })}
              {/* Placeholder cards to fill the row */}
              {Array.from({
                length: (4 - (availableCourses?.length % 4)) % 4
              }).map((_, index) => (
                <div key={`placeholder-${index}`} className={clsx('flex justify-content-center align-items-center', styles.card)}>
                  <Image src={emptyCard} alt="Empty Course Placeholder" />
                </div>
              ))}
            </div>
            <ConditionalDisplay condition={availableCoursesTotalCount > 1}>
              <div className="flex justify-content-center mt-5">
                <Button
                  onClick={() => router.push('/LMS/AvailableCourses')}
                  disabled={availableCoursesLoading}
                  theme="metronic"
                  variant="outline"
                  label={availableCoursesLoading ? 'Loading...' : 'Show More Courses'}
                />
              </div>
            </ConditionalDisplay>
          </div>
        </ConditionalDisplay>
      </div>

      {/* Tables Section */}
      {[
        {
          title: 'Ongoing Classes',
          data: ongoingCoursesData,
          type: 'ongoing',
          emptyMessage: 'You have no courses enrolled at the moment'
        },
        {
          title: 'Pending Quizzes',
          data: pendingQuizzesData,
          type: 'quiz',
          emptyMessage: 'You have no pending quizzes at the moment'
        },
        {
          title: 'Your Certification',
          data: certificatesData,
          type: 'certificate',
          emptyMessage: 'You have no certificates at the moment'
        }
      ].map(({ title, data, type, emptyMessage }, index) => (
        <div
          style={{
            marginTop: '1.25rem',
            marginBottom: '1.25rem'
          }}
          key={index}
        >
          <DataTable
            header={() => <TableHeader text={title} />}
            className="custom-lead"
            style={{ cursor: 'pointer' }}
            showHeaders={false}
            value={data.rows}
            paginator={data.totalCount > 0}
            rows={data.lazyParams.rows}
            totalRecords={data.totalCount}
            lazy
            first={data.lazyParams.first}
            onPage={data.onPage}
            onSort={data.onSort}
            sortField={data.lazyParams.sortField}
            sortOrder={data.lazyParams.sortOrder}
            emptyMessage={() => <EmptyDataTemplate message={emptyMessage} />}
          >
            {getTableColumns(type).map((col, index) => (
              <Column key={index} {...col} />
            ))}
          </DataTable>
        </div>
      ))}
    </PageContainer>
  )
}
