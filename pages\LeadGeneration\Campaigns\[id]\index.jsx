import { useState, useEffect, useRef } from 'react'
import { Tab } from '../../../../components/UI/Tabs/Tabs'
import Button from '../../../../components/UI/Button/Button'
import styles from '../../index.module.css'
import SelectInput from '../../../../components/UI/Input/SelectInput/SelectInput'
import clsx from 'clsx'
import BreadCrumbs from '../../../../components/UI/BreadCrumbs/BreadCrumbs'
import { InputText } from 'primereact/inputtext'
import TextInput from '../../../../components/UI/Input/TextInput/TextInput'
import { useRouter } from 'next/router'
import Image from 'next/image'
import Backarrow from '../../../../svg/metronic/back_metronic.svg'
import Editor from '../../../../components/LexicalEditor/LexicalEditor'
import { $generateHtmlFromNodes } from '@lexical/html'
import tStyle from '../../../template/template.module.css'
import Modal from '../../../../components/UI/Modal/Modal'
import TextareaInput from '../../../../components/UI/Input/TextareaInput/TextareaInput'
// import AnalyticsDashboard from "../../../components/UI/Dashboards/LeadGenerationDashboard/AnalyticsDashboard";
import { FileUpload } from 'primereact/fileupload'
import useFileUploadOptions from '../../../../hooks/useFileUploadOptions'
import EmailTemplates from '../../../../components/UI/Templates/EmailTemplates/EmailTemplates'
import AnalyticsDashboard from '../../../../components/UI/Dashboards/LeadGenerationDashboard/AnalyticsDashboard'
import { useAccount, useMsal } from '@azure/msal-react'
import { getAccessTokenForScopeSilent } from '../../../../src/GetAccessTokenForScopeSilent'
import { formBuilderApiRequest } from '../../../../src/msalConfig'
import { useQuery } from '@tanstack/react-query'
import { MultiSelect } from 'primereact/multiselect'

const api = process.env.NEXT_PUBLIC_FORM_BUILDER_API

// Mock campaign data
const mockCampaign = {
  id: 'CMP-2025-001',
  name: 'Spring Promo Campaign',
  description: 'A campaign to promote spring offers via email.',
  owner: 'Steve Hayden',
  ownerEmail: '<EMAIL>',
  status: 'Active',
  channel: 'Email'
}

// Mock users data
const mockUsers = [
  {
    label: 'Steve Hayden',
    value: 'Steve Hayden',
    email: '<EMAIL>'
  },
  {
    label: 'Tommy Evans',
    value: 'Tommy Evans',
    email: '<EMAIL>'
  },
  { label: 'Jane Smith', value: '<EMAIL>' },
  { label: 'Alice Johnson', value: '<EMAIL>' }
]

const defaultCampaign = {
  id: '',
  name: '',
  description: '',
  owner: '',
  ownerEmail: '',
  status: '',
  channel: ''
}

export default function Index() {
  const [currentTab, setCurrentTab] = useState(0)
  const { accounts } = useMsal()
  const account = useAccount(accounts[0] ?? {})
  const router = useRouter()
  const { id } = router.query
  const [campaign, setCampaign] = useState({})

  const [loading, setLoading] = useState(false)
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true)

        const accessToken = await getAccessTokenForScopeSilent(formBuilderApiRequest)

        const response = await fetch(`${api}Campaigns/${id}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${accessToken}`
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()
        setCampaign(data)
      } catch (error) {
        console.error('Error fetching campaign data:', error)
        setCampaign(defaultCampaign)
      } finally {
        setLoading(false)
      }
    }

    if (account && id) {
      loadData()
    }
  }, [id])

  const TABS = [
    {
      value: 0,
      title: 'Basic Info'
    },
    { value: 1, title: 'Template' }, //TemplateBuilderTab EmailTemplates
    {
      value: 2,
      title: 'Delivery Details'
    },
    {
      value: 3,
      title: 'Analytics'
    },
    {
      value: 4,
      title: 'Audit History'
    }
  ]

  return (
    <>
      <div className={styles.pageContainer}>
        {/* Header with BreadCrumbs */}
        <div className="flex justify-content-between align-items-center" style={{ padding: '24px 0 0 0' }}>
          <BreadCrumbs
            title="Campaign Details"
            breadcrumbItems={[
              { label: 'Home', url: '/' },
              { label: 'Lead Generation', url: '/LeadGeneration' },
              { label: 'Campaigns', url: '/LeadGeneration/Canpaigns' },
              { label: mockCampaign.name, url: '' }
            ]}
            theme="metronic"
          />
          <Button label="Save" theme="metronic" style={{ minWidth: 100, fontWeight: 600 }} onClick={() => {}} />
        </div>
        <div className={styles.tabContainer} style={{ marginTop: 24, borderBottom: '1px solid #e9ecef' }}>
          <Button icon={<Image src={Backarrow} alt="Back" />} onClick={() => router.back()} className={styles.iconButton} />
          <div style={{ display: 'flex', gap: '2.5rem' }}>
            {TABS.map((tab) => (
              <Tab
                key={tab.value}
                value={tab.value}
                title={tab.title}
                display={true}
                isActive={currentTab === tab.value}
                handleClick={(e, value) => setCurrentTab(value)}
                theme="metronic"
              />
            ))}
          </div>
        </div>
        {currentTab === 0 && <CampaignBasicInfo campaign={campaign} setCampaign={setCampaign} />}
        {currentTab === 1 && <TemplateBuilderTab />}
        {currentTab === 2 && <DeliveryDetailsTab />}
        {currentTab === 3 && <AnalyticsDashboard />}
        {currentTab === 4 && <div>Audit History Tab Content</div>}
      </div>
    </>
  )
}

function CampaignBasicInfo({ campaign, setCampaign }) {
  const fetchContactLists = async () => {
    const accessToken = await getAccessTokenForScopeSilent(formBuilderApiRequest)
    const response = await fetch(`${api}LeadGenerationContactList`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`
      }
    })
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const data = await response.json()
    return data
  }

  const { data: leadGenerationContactLists } = useQuery({ queryKey: ['leadGenerationContactLists'], queryFn: fetchContactLists })

  const getTotalContactCount = () => {
    if (!campaign.leadGenerationContactLists || campaign.leadGenerationContactLists.length === 0) {
      return 0
    }
    return campaign.leadGenerationContactLists.reduce((total, list) => total + (Array.isArray(list.contacts) ? list.contacts.length : 0), 0)
  }

  console.log('Lead Generation Contact Lists:', leadGenerationContactLists)
  console.log('campaign:', campaign)

  return (
    <div className="flex-grow-1">
      <div className={clsx('p-0 ', styles.gridCard)} style={{ height: '98%' }}>
        <div className={clsx('text-center', styles.formTitle)}>General Information</div>
        <div className={styles.formSide} style={{ marginRight: '12rem', marginLeft: '12rem' }}>
          <div className="flex w-full gap-4 mt-4">
            <TextInput
              value={campaign.id || ''}
              label="Campaign ID"
              onChange={(e) => setCampaign((prev) => ({ ...prev, id: e.target.value }))}
              theme="metronic"
            />
            <SelectInput
              label="Status"
              value={campaign.status || 0}
              options={[
                { label: 'Draft', value: 0 },
                { label: 'Scheduled', value: 1 },
                { label: 'Sent', value: 2 },
                { label: 'Cancelled', value: 3 }
              ]}
              onChange={(e) => setCampaign((prev) => ({ ...prev, status: e.value }))}
              theme="metronic"
            />
          </div>
          <div className="flex w-full gap-4 mt-4">
            <TextInput
              label="Campaign Name"
              value={campaign.name || ''}
              onChange={(e) => setCampaign((prev) => ({ ...prev, name: e.target.value }))}
              theme="metronic"
            />
            <SelectInput
              label="Channel"
              value={campaign.channel || 0}
              options={[
                { label: 'Email', value: 0 },
                { label: 'SMS', value: 1 },
                { label: 'Social', value: 2 }
              ]}
              onChange={(e) => setCampaign((prev) => ({ ...prev, channel: e.value }))}
              style={{ width: '100%' }}
              theme="metronic"
              placeholder="Select Channel"
            />
          </div>
          <div className="flex w-full gap-4 mt-4">
            <TextInput
              label="Description"
              value={campaign.description || ''}
              onChange={(e) => setCampaign((prev) => ({ ...prev, description: e.target.value }))}
              style={{ width: '100%' }}
              theme="metronic"
            />
            <TextInput label="Owner/Manager Email" value={campaign.ownerEmail} theme="metronic" />
          </div>
          <div className="flex w-full gap-4 mt-4">
            <div className="flex flex-column" style={{ width: '49%', rowGap: '10px' }}>
              <div className="metronicLabel">Contact Lists</div>
              <MultiSelect
                className="custom-lead metronicCalendarInput"
                value={campaign.leadGenerationContactLists || []}
                key={campaign.id}
                optionLabel="name"
                options={leadGenerationContactLists}
                onChange={(e) => setCampaign((prev) => ({ ...prev, leadGenerationContactLists: e.value }))}
                theme="metronic"
                style={{ width: '100%', minHeight: '45px', border: '1px solid #5151511a' }}
              />
            </div>
            <div className="flex flex-column" style={{ width: '49%' }}>
              <TextInput label="Total Contacts" value={getTotalContactCount()} theme="metronic" disabled />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Mock data for Delivery Details tab
function DeliveryDetailsTab() {
  const [subject] = useState('Spring Offers Launch')
  const [deliveryOption] = useState('Schedule for later')
  const [from] = useState('<EMAIL>')
  const [deliveryDate] = useState('05/15/2025')
  const [deliveryTime] = useState('10:00 AM')

  return (
    <div className="flex-grow-1">
      <div className={clsx('p-0 ', styles.gridCard)} style={{ height: '98%' }}>
        <div className={clsx('text-center', styles.formTitle)}> Delivery Details</div>
        <div className={styles.formSide} style={{ marginRight: '12rem', marginLeft: '12rem' }}>
          <div className="flex w-full gap-4 mt-4">
            <TextInput label="Subject" value={subject} disabled theme="metronic" />
            <TextInput label="From" value={`${from} `} disabled theme="metronic" />
          </div>
          <div className="flex w-full gap-4 mt-4">
            <TextInput label=" Delivery Options" value={`${deliveryOption}`} disabled theme="metronic" />
            <TextInput label="Delivery Time" value={`${deliveryTime}`} disabled theme="metronic" />
          </div>
          <div className="mt-4" style={{ width: '49%' }}>
            {' '}
            <TextInput label=" Delivery Date" value={`${deliveryDate}`} disabled theme="metronic" />
          </div>
        </div>
      </div>
    </div>
  )
}

function TemplateBuilderTab() {
  // Mock state for template and dollarVariable
  const [template, setTemplate] = useState({
    jsonValue: '',
    jsonTemplate: '<div>Sample Email Content</div>',
    file: '',
    module: 'Form'
  })
  const [dollarVariable, setDollarVariable] = useState([
    { label: 'First Name', value: '$firstName' },
    { label: 'Last Name', value: '$lastName' },
    { label: 'Email', value: '$email' }
  ])
  const [error] = useState({})
  const [solutionId] = useState(1)

  const previewRef = useRef(null)
  const embedRef = useRef(null)
  const previewRefs = useRef(null)
  const [prevHtml, setPrevHtml] = useState('')
  const [footer, setFooter] = useState('&#169; 2025 Revvdd All Rights')
  const value = template.jsonValue
  let instance = null

  useEffect(() => {
    if (template) {
      // const htmlBlob = new Blob([template.jsonTemplate], { type: 'text/html' })
      // embedRef.current.src = URL.createObjectURL(htmlBlob)
      previewRefs.current.innerHTML = template.jsonTemplate
    }
    if (template.module) {
      // axiosGet(
      //   `NotificationTemplate/${template.module === "Form" ? 1 : 6}/${
      //     template.module
      //   }/Variables`
      // )
      //   .then((res) => {
      //     setDollarVariable(res.data);
      //   })
      //   .catch(() => {
      //     Toast.current.show({
      //       severity: "error",
      //       summary: "Dollar variable in unavailable.",
      //       life: 3000,
      //     });
      //   });
    }
  }, [])

  useEffect(() => {
    if (template.file) {
      previewRef.current.style.backgroundImage = `Url(${template.file})`
    }
  }, [template])

  const setBlobValue = (html, footer, json) => {
    const emailTemplate = `
            <style>
            .editor-paragraph {
                padding: 0;
                margin: 0;
                margin-bottom: 2px;
                position: relative;
              }
            .box{
                box-shadow: 0px 3px 99px #9B949429;
                color:#024f7c;
                font-size: 1rem;
                transform: translateY(-5px);
              }
            .tempBtn{
                font-size: 1rem;
                background-color: #024f7c;
                color:#fff;
                outline: none;
                border: none;
                padding: 12px;
                border-radius: 5px;
                text-decoration: none;
                min-width: 200px;
                }
            </style>
            <main style="background-color: #fff;height:100%;width:100%;color:#024f7c;font-family:Work Sans; font:16px;" >
            <div class="box" style="border-radius:10px 10px 10px 10px;">
            <div style="display:'block';background-color:#024f7c;padding:.5rem; color:#fff; border-radius:10px 10px 0 0;" >
            </div>
            <div style="overflow:auto; padding:1rem;background-position: center;background-repeat:no-repeat;background-size:100% 100%;background-image:url(https://www.freepik.com/premium-vector/christmas-blue-snowflakes-blizzard-stream-light_33087411.htm#fromView=search&term=email+background&track=ais&regularType=vector&page=1&position=45&uuid=d296219b-53f8-4ef1-affb-8464bd4e339c)" >                                
                <div>
                   ${html}
                </div>
                <br>
            </div>
        <div style="overflow:hidden;display:block;background-color:#024f7c;padding:.5rem; color:#fff; border-radius:0px 0px 10px 10px;" >
        <center style="text-overflow: ellipsis;">
          ${footer}
        </center>
        </div>
            </div>
        </main>
        `
    const backendTemplate = emailTemplate.replaceAll(
      '[Logo]',
      `<Img alt='LOGO' src="[Logo]" height="100px" width="200px" style="object-fit: contain;" />`
    )
    const renderTemplate = emailTemplate.replaceAll(
      '[Logo]',
      `<Img alt='LOGO' src="${logoUrl}" height="100px" width="200px" style="object-fit: contain;" />`
    )
    if (previewRefs?.current) {
      setTemplate((prev) => ({
        ...prev,
        jsonTemplate: backendTemplate.trim(),
        jsonValue: json
      }))
      previewRefs.current.innerHTML = renderTemplate
    }
  }

  const cancel = (instance) => {
    if (instance !== null) {
      clearTimeout(instance)
    }
  }

  const changeHandler = (name, valueAsJson, transformers, editor, event) => {
    // cancel(instance)
    let foot
    if (event) {
      const { value } = event.target
      foot = value
      setFooter(value)
    }
    if (editor) {
      setPrevHtml($generateHtmlFromNodes(editor))
    }

    const html = editor ? $generateHtmlFromNodes(editor) : prevHtml

    setBlobValue(html, foot ? foot : footer, valueAsJson)
  }

  const [logoUrl, setLogoUrl] = useState(template.file)

  const templateOptions = [
    { label: 'Monthly Email Campaign', value: 'monthly' },
    { label: 'Weekly Newsletter', value: 'weekly' },
    { label: 'Product Launch', value: 'launch' }
  ]

  const router = useRouter()

  const { channel, owner, details } = router.query
  const acceptedFiles = 'image/*'
  const { chooseOptions, uploadOptions, cancelOptions, emptyTemplate, itemTemplate, headerTemplate } = useFileUploadOptions()

  return (
    <>
      <div className="flex" style={{ gap: '1.875rem' }}>
        <div className={clsx('p-0', styles.gridCard)} style={{ width: '60%' }}>
          <div className={styles.formTitle}> Template Information</div>
          <div style={{ padding: '30px' }}>
            <SelectInput
              label="Template Name"
              value={template}
              options={templateOptions}
              onChange={(e) => setTemplate(e.value)}
              theme="metronic"
              placeholder="Select Template"
              style={{ width: '100%' }}
            />
            <div className={styles.fontText} style={{ marginTop: 32, fontWeight: 700, fontSize: 16 }}>
              Template
            </div>

            {channel === 'SMS' ? (
              <TextareaInput theme={'metronic'} placeholder={'Type here'} rows={3} />
            ) : (
              <>
                <div className="flex flex-column gap-3 mb-4">
                  <label className={styles.fontText}>Logo</label>
                  <FileUpload
                    name="demo[]"
                    ref={previewRef}
                    multiple
                    webkitdiretory="true"
                    maxFileSize={25000000}
                    itemTemplate={itemTemplate}
                    headerTemplate={headerTemplate}
                    chooseOptions={{
                      className: 'custom-choose-btn-secondary'
                    }}
                    chooseLabel="Choose File"
                    cancelOptions={cancelOptions}
                    emptyTemplate={emptyTemplate}
                    customUpload={true}
                    uploadHandler={(e) => {
                      const file = e.files[0]
                      if (file) {
                        const imageUrl = URL.createObjectURL(file)
                        previewRef.current.style.backgroundImage = `url(${imageUrl})`

                        setTemplate((prev) => ({
                          ...prev,
                          file: file
                        }))
                        setLogoUrl(imageUrl)
                      }
                    }}
                    uploadOptions={uploadOptions}
                    accept={acceptedFiles}
                  />
                </div>
                <div className={tStyle.inputWidthContainer}>
                  <label
                    style={{
                      fontSize: '15px',
                      fontWeight: 'bold',
                      color: 'var(--primary-bg-blackPearl'
                    }}
                  >
                    Content{<span className={tStyle.starSymbol}> *</span>}
                  </label>
                  <Editor
                    key={dollarVariable}
                    value={value || false}
                    mentionsLists={dollarVariable}
                    name={'caption'}
                    onChange={
                      (name, valueAsJson, transformers, editor) => {}
                      // changeHandler(name, valueAsJson, transformers, editor)
                    }
                  />
                  {error.jsonTemplate !== '' && <small className={tStyle.starSymbol}>{error.jsonTemplate}</small>}
                </div>
                <TextInput
                  name={'footer'}
                  label="Footer"
                  value={footer}
                  onChange={(e) => changeHandler(undefined, undefined, undefined, undefined, e)}
                  theme="metronic"
                />
              </>
            )}
          </div>
        </div>
        <div className={clsx('p-0', styles.gridCard)} style={{ width: '40%' }}>
          <div className={styles.formTitle}>Preview</div>
          <div className={tStyle.previewBox} style={{ padding: '30px' }}>
            <div className={tStyle.previewContent} ref={previewRefs}></div>
          </div>
        </div>
      </div>
    </>
  )
}
