import React, { useEffect, useState } from 'react'
import Head from 'next/head'
import BreadCrumbs from '../../../components/UI/BreadCrumbs/BreadCrumbs'
import styles from '../index.module.css'
import clsx from 'clsx'
import { <PERSON><PERSON><PERSON>, Pie, Cell, Tooltip } from 'recharts'
import Button from '../../../components/UI/Button/Button'
import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import { InputText } from 'primereact/inputtext'
import dashboardStyles from '../../../components/UI/DashboardsSearchbar/DashboardsSearchbar.module.css'
import { useRouter } from 'next/router'
import { Dialog } from 'primereact/dialog'
import TextInput from '../../../components/UI/Input/TextInput/TextInput'
import SelectInput from '../../../components/UI/Input/SelectInput/SelectInput'
import TextareaInput from '../../../components/UI/Input/TextareaInput/TextareaInput'
import Modal from '../../../components/UI/Modal/Modal'
import eye from '../../../svg/metronic/edit_eye.svg'
import Image from 'next/image'
import commonProfile from '../../../svg/metronic/blue_profile.svg'
import active from '../../../svg/metronic/camp_active.svg'
import schedule from '../../../svg/metronic/camp_schedule.svg'
import complete from '../../../svg/metronic/camp_complete.svg'
import { useDashboard } from '../../../hooks/useDashboard'
import { getAccessTokenForScopeSilent } from '../../../src/GetAccessTokenForScopeSilent'
import { formBuilderApiRequest } from '../../../src/msalConfig'
import { useAccount, useMsal } from '@azure/msal-react'
import { DateColumnTemplate } from '../../../components/UI/Dashboards/UI/DateColumnTemplate/DateColumnTemplate'

const api = process.env.NEXT_PUBLIC_FORM_BUILDER_API

// Reuse SummaryCard from LeadGeneration/index.jsx
function SummaryCard({ label, value, className, icon, percent, color }) {
  return (
    <div className={`${styles.campaignCard} ${(className ?? '') + ' flex justify-content-between'}`}>
      <div className="flex flex-column justify-content-center pl-5">
        <div>
          {/* Use PrimeIcons */}
          {icon && <Image src={icon} alt="image" />}
        </div>

        <div className={clsx('mt-3 mb-2', styles.summaryCardValue)}>{value}</div>
        <div className={styles.summaryCardLabel}>{label}</div>
      </div>
      {percent && <span className={clsx('pt-4 pr-4', styles.summaryCardPercent)}>{percent}</span>}
    </div>
  )
}

// Use PrimeIcons for summary cards
const summaryCards = [
  { label: 'Active', value: 24, icon: <Image src={active} alt="active" />, color: '#2d5fff' },
  { label: 'Scheduled', value: 2, icon: 'pi-calendar', color: '#ffb14e' },
  { label: 'Completed', value: 15, icon: 'pi-check', color: '#43c59e' }
  // {
  //   label: "New Applications This Week",
  //   value: 24,
  //   icon: "pi-user-plus",
  //   color: "#f7b500",
  // },
  // {
  //   label: "Completed Applications This Week",
  //   value: 2,
  //   icon: "pi-users",
  //   color: "#ff6f61",
  // },
  // {
  //   label: "Applications In Progress",
  //   value: 15,
  //   icon: "pi-spinner",
  //   color: "#8884d8",
  // },
]

// Chart data
const campaignsByChannel = [
  { name: 'SMS', value: 29, color: '#2d5fff' },
  { name: 'Social', value: 14, color: '#ffb14e' },
  { name: 'Email', value: 128, color: '#f7b500' },
  { name: 'Ads', value: 42, color: '#ff6f61' }
]

export default function Index() {
  const [showModal, setShowModal] = useState(false)

  return (
    <>
      <div className={styles.pageContainer}>
        <Head>
          <title>Campaigns Dashboard</title>
        </Head>
        <div className="flex justify-content-between">
          <BreadCrumbs
            title="Campaigns Dashboard"
            breadcrumbItems={[
              { label: 'Home', url: '/' },
              { label: 'Lead Generation', url: '/LeadGeneration' },
              { label: 'Campaigns', url: '/LeadGeneration/Canpaigns' }
            ]}
            theme="metronic"
          />
        </div>
        <div className={styles.summaryCardsGrid}>
          <SummaryCard label="Active" value={24} percent="+12%" icon={active} className="flex justify-content-between" />
          <SummaryCard label="Scheduled" value={2} percent="+12%" icon={schedule} className="flex justify-content-between" />
          <SummaryCard label="Completed" value={2} percent="+12%" icon={complete} className="flex justify-content-between" />
          <div className={styles.campaignCard} style={{ width: '40%' }}>
            <div className="flex">
              <div className="flex flex-column">
                <h3 className={clsx('text-lg font-bold', styles.fontText)}>Campaigns by Channel</h3>

                <PieChart width={320} height={200}>
                  <Pie
                    data={campaignsByChannel}
                    dataKey="value"
                    nameKey="name"
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={90}
                    paddingAngle={2}
                  >
                    {campaignsByChannel.map((entry) => (
                      <Cell key={entry.name} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </div>
              <div className="flex flex-column mt-5 gap-4 justify-content-center">
                {campaignsByChannel.map((entry) => (
                  <span
                    key={entry.name}
                    style={{
                      display: 'inline-block',
                      margin: '0 10px',
                      color: entry.color,
                      fontWeight: 600
                    }}
                  >
                    <span
                      style={{
                        display: 'inline-block',
                        width: 10,
                        height: 10,
                        borderRadius: '50%',
                        background: entry.color,
                        marginRight: 6,
                        verticalAlign: 'middle'
                      }}
                    />
                    {entry.name} {entry.value}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
        <CampaignsDashboard setShowModal={setShowModal} />
        <CreateCampaignModal showModal={showModal} setShowModal={setShowModal} />
      </div>
    </>
  )
}

const CampaignsDashboard = ({ setShowModal }) => {
  const { accounts } = useMsal()
  const account = useAccount(accounts[0] ?? {})
  const router = useRouter()
  const { rows, setRows, totalCount, setTotalCount, lazyParams, globalFilter, onSort, onPage, onFilter, onGlobalFilterChange } =
    useDashboard({ defaultSortField: 'lastUpdatedAtUtc' })

  const [loading, setLoading] = useState(false)
  useEffect(() => {
    const loadLazyData = async () => {
      try {
        setLoading(true)

        const accessToken = await getAccessTokenForScopeSilent(formBuilderApiRequest)

        const response = await fetch(`${api}Campaigns/Filter`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${accessToken}`
          },
          body: JSON.stringify({ ...lazyParams, globalFilter })
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        const data = await response.json()

        setRows(data?.campaigns || [])
        setTotalCount(data?.count)
      } catch (error) {
        console.error('Error loading lazy data:', error)
        setRows([])
        setTotalCount(0)
      } finally {
        setLoading(false)
      }
    }

    if (account) {
      loadLazyData()
    }
  }, [lazyParams, globalFilter])

  const tableheader = () => (
    <div className={styles.prospecTable}>
      <span className="text-xl font-bold">Campaigns</span>
      <div className="flex" style={{ gap: '20px' }}>
        <span
          className="p-input-icon-left"
          style={{
            display: 'flex',
            flexDirection: 'row-reverse'
          }}
        >
          <i className={clsx('pi pi-search', styles.searchIcon)} style={{ top: '22px' }} />
          <InputText placeholder="Search" className={styles.search} />
        </span>
        <Button onClick={() => setShowModal(true)} label={'Create New'} variant={'fill'} width="10rem" theme="metronic" />
      </div>
    </div>
  )

  const actionBodyTemplate = (rowData) => {
    return <Image src={eye} alt="eye" onClick={() => router.push(`Campaigns/${rowData.id}`)} className="cursor-pointer" />
  }

  const statusTemplate = (rowData) => {
    const isActive = rowData.statusName !== 'Draft'
    console.log('rowData', rowData)

    return (
      <div
        className={clsx(
          'flex align-items-center gap-2 px-3 py-2 border-round-3xl ',
          isActive ? styles.approveContainer : styles.inprogressContainer
        )}
      >
        <span className={isActive ? styles.approveCircle : styles.inprogressCircle}></span>
        <span title={rowData.statusName} className={styles.truncateCell}>
          {rowData.statusName}
        </span>
      </div>
    )
  }

  const idColumnTemplate = (rowData) => {
    const shortId = rowData.id ? String(rowData.id).slice(0, 6) : ''
    return <span className={styles.idText}>{shortId}</span>
  }

  return (
    <div>
      <DataTable
        className="custom-lead mt-5"
        value={rows}
        lazy
        dataKey={'id'}
        paginator
        first={lazyParams.first}
        rows={lazyParams.rows}
        totalRecords={totalCount}
        onPage={onPage}
        onSort={onSort}
        sortField={lazyParams.sortField}
        sortOrder={lazyParams.sortOrder}
        onFilter={onFilter}
        loading={loading}
        header={tableheader}
      >
        <Column field="action" header="Action" body={actionBodyTemplate} />
        <Column field="id" header="ID" body={idColumnTemplate} />
        <Column field="name" header="Name" />
        <Column field="status" header="Status" body={statusTemplate} />
        <Column field="channelName" header="Channel" />
        <Column field="contactCount" header="Audience Size" />
        <Column field="ownerEmail" header="Owner/Manager" />
        <Column field="createdAt" header="Created Date" body={(rowData) => <DateColumnTemplate date={rowData?.createdAt} />} />
        <Column field="updatedAt" header="Last Updated" body={(rowData) => <DateColumnTemplate date={rowData?.lastUpdatedAt} />} />
      </DataTable>
    </div>
  )
}

const CreateCampaignModal = ({ showModal, setShowModal }) => {
  const router = useRouter()
  const [campaignForm, setCampaignForm] = useState({
    name: '',
    channel: 0,
    description: '',
    ownerEmail: '<EMAIL>'
  })

  const channelOptions = [
    { label: 'Email', value: 0 },
    { label: 'SMS', value: 1 },
    { label: 'Social (single)', value: 2 }
  ]

  const ownerOptions = [
    {
      label: 'Steve Hayden',
      value: '<EMAIL>'
    },
    {
      label: 'Tommy Evans',
      value: '<EMAIL>'
    },
    { label: 'Jane Smith', value: '<EMAIL>' },
    { label: 'Alice Johnson', value: '<EMAIL>' }
  ]

  const [loading, setLoading] = useState(false)
  const handleSave = async () => {
    try {
      setLoading(true)

      const accessToken = await getAccessTokenForScopeSilent(formBuilderApiRequest)

      const res = await fetch(`${api}Campaigns`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`
        },
        body: JSON.stringify(campaignForm)
      })

      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status}`)
      }

      const json = await res.json()
      router.push(`/LeadGeneration/Campaigns/${json.id}`)
    } catch (error) {
      console.error('Error saving campaign:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Modal
      theme="metronic"
      header={'New Campaign'}
      visible={showModal}
      width={60}
      onHide={() => setShowModal(false)}
      style={{ backgroundColor: '#fff', width: '60%' }}
    >
      <div className="p-fluid" style={{ display: 'flex', gap: 24, marginBottom: 16 }}>
        <div style={{ flex: 1 }}>
          <TextInput
            id="campaignName"
            label="Name"
            value={campaignForm.name}
            onChange={(e) => setCampaignForm((prev) => ({ ...prev, name: e.target.value }))}
            theme="metronic"
            required
          />
        </div>
        <div style={{ flex: 1 }}>
          <SelectInput
            id="campaignChannel"
            label="Channel"
            value={campaignForm.channel}
            options={channelOptions}
            onChange={(e) => setCampaignForm((prev) => ({ ...prev, channel: e.value }))}
            optionLabel="label"
            optionValue="value"
            theme="metronic"
            required
          />
        </div>
      </div>
      <div style={{ marginBottom: 16 }}>
        <TextareaInput
          id="campaignDetails"
          label="Description"
          value={campaignForm.description}
          onChange={(e) => setCampaignForm((prev) => ({ ...prev, description: e.target.value }))}
          theme="metronic"
          required
          height="100px"
          containerHeight="100px"
        />
      </div>
      <div>
        <SelectInput
          id="campaignOwner"
          label="Owner/Manager"
          value={campaignForm.ownerEmail}
          options={ownerOptions}
          onChange={(e) => setCampaignForm((prev) => ({ ...prev, ownerEmail: e.value }))}
          placeholder="Select Owner"
          optionLabel="label"
          optionValue="value"
          theme="metronic"
          required
        />
      </div>
      <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
        <Button label="Save" theme="metronic" loading={loading} onClick={handleSave} style={{ minWidth: 120, marginTop: 16 }} />
      </div>
    </Modal>
  )
}
