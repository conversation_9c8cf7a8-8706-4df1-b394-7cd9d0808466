import React, {
  useContext,
  useEffect,
  useState,
  useRef,
  useDeferredValue,
  Suspense,
  lazy,
  forwardRef,
} from 'react'
import { Tab, TabContainer } from '../../../components/UI/Tabs/Tabs'
import Button from '../../../components/UI/Button/Button'
import Backarrow from '../../../svg/metronic/back_metronic.svg'
import leftarrow from '../../../svg/metronic/leftArrow.svg'
import rightarrow from '../../../svg/metronic/rightArrow.svg'
import Image from 'next/image'
import styles from '../index.module.css'
import { PageContainer } from '../../../components/UI/Page/PageContainer/PageContainer'
import TextareaInput from '../../../components/UI/Input/TextareaInput/TextareaInput'
import TextInput from '../../../components/UI/Input/TextInput/TextInput'
import UserProfile from '../../../svg/metronic/blue_profile.svg'
import SelectInput from '../../../components/UI/Input/SelectInput/SelectInput'
import { ConditionalDisplay } from '../../../components/UI/ConditionalDisplay/ConditionalDisplay'
import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import { useRouter } from 'next/router'
import LeadGenerationModal from '../../../components/LeadGeneration/LeadGenerationModal'
import ConvertModal from '../../../components/LeadGeneration/ConvertModal'
import { PopupButton } from 'react-calendly'
import style from '../../../components/UI/Button/Button.module.css'
import clsx from 'clsx'
import dashboardStyles from '../../../components/UI/DashboardsSearchbar/DashboardsSearchbar.module.css'
import { InputText } from 'primereact/inputtext'
import LeadGenerationWorkflow from '../Workflow/LeadGenerationWorkflow'
import { useGetMeetingToken } from '../../../src/useGetMeetingToken'
import { getLeadGenerationById } from '../../../api/apiCalls'
import { getAccessToken } from '../../../utillites/getAccessToken'
import useUtilityFunctions from '../../../hooks/useUtilityFunctions'
import moment from 'moment'
import { useApi } from '../../../hooks/useApi'
import { data } from 'autoprefixer'
import { InputSwitch } from 'primereact/inputswitch'
import { Button as PrimeButton } from 'primereact/button'
import closeIcon from '../../../svg/metronic/close.svg'
import { useApiQuery } from '../../../api/apiRequest'
import AutoComplete from '../../../components/UI/Input/AutoComplete/AutoComplete'
import UserProfileContext from '../../../public/UserProfileContext/UserProfileContext'
import { PhoneCallComponent } from '../../../components/UI/PhoneCallComponent/PhoneCallComponent'
import { useAccount, useMsal } from '@azure/msal-react'
import { useDashboard } from '../../../hooks/useDashboard'
import { formBuilderApiRequest } from '../../../src/msalConfig'
import { getAccessTokenForScopeSilent } from '../../../src/GetAccessTokenForScopeSilent'
import { DateTimeColumnTemplate } from '../../../components/UI/Dashboards/UI/DateColumnTemplate/DateColumnTemplate'
import { Guid } from 'js-guid'
import { SendSmsButton } from '../../../components/UI/SendSmsButton/SendSmsButton'
import { MailComponent } from '../../../components/UI/MailComponent/MailComponent'
import { useHandleTimelineEvent } from '../../../hooks/LeadGeneration/useHandleTimelineEvent'
import { useFetchLeadDetails } from '../../../hooks/LeadGeneration/useFetchLeadDetails'
import parse from 'html-react-parser'
import mail from '../../../svg/metronic/mail.svg'
import mailDisable from '../../../svg/metronic/mail_disable.svg'
import '@toast-ui/calendar/dist/toastui-calendar.min.css'
import { Toast } from 'primereact/toast'
import close from '../../../svg/metronic/close_toast.svg'
import reject from '../../../svg/metronic/reject.svg'
import success from '../../../svg/metronic/success.svg'
const Calendar = lazy(() => import('@toast-ui/react-calendar'))
import { ProgressBar } from 'primereact/progressbar'
import { downloadBase64File } from '../../../utillites/utils'

// let dayCount = 0;
import eye from '../../../svg/metronic/edit_eye.svg'
import edit from '../../../svg/metronic/edit_pencil.svg'
import deleteIcon from '../../../svg/metronic/delete.svg'
import download from '../../../svg/metronic/download.svg'
import {
  Education,
  EducationHistory,
} from '../../../components/LeadGeneration/LeadGenerationTabs/EducationHistory'
import { EngagementLog } from '../../../components/LeadGeneration/LeadGenerationTabs/EngagementLog'
import { PendingTasks } from '../../../components/LeadGeneration/LeadGenerationTabs/Tasks'
import { AuditHistory } from '../../../components/LeadGeneration/LeadGenerationTabs/AuditHistory'
import { BasicInfo } from '../../../components/LeadGeneration/LeadGenerationTabs/BasicInfo'
import { Applications } from '../../../components/LeadGeneration/LeadGenerationTabs/Applications'
import { Appointments } from '../../../components/LeadGeneration/LeadGenerationTabs/Appointments'
import { Programs } from '../../../components/LeadGeneration/LeadGenerationTabs/Programs'
import { CertificateBuilder } from '../../../components/LeadGeneration/LeadGenerationTabs/CertificateBuilder'

const api = process.env.NEXT_PUBLIC_FORM_BUILDER_API

function parseHtml(html) {
  return parse(html)
}

function formatAppointment(data) {
  let formattedTime
  try {
    // Parse the time in UTC to prevent timezone conversion
    const parsedTime = moment.utc(data.startTime)
    if (!parsedTime.isValid()) {
      // If not a valid date/time, try parsing as time only
      const timeOnly = moment(data.startTime, ['HH:mm', 'h:mm A'])
      formattedTime = timeOnly.isValid()
        ? timeOnly.format('h:mm A')
        : 'Invalid time'
    } else {
      // Valid date/time, format with date and time
      formattedTime = parsedTime.format('M/D/YYYY h:mm A')
    }
  } catch (error) {
    formattedTime = 'Invalid time'
  }

  return `<b>Appointment Name:</b> ${data.title}<br/>
<b>Description:</b> ${data.description}<br/>
<b>Start Time:</b> ${formattedTime}<br/>`
}

const template = {
  task(event) {
    return `<span style="color: red;">${event.title}</span>`
  },
  time(event) {
    return `<div style="display: flex; flex-direction: row; justify-content: space-between; align-items: flex-end; background: #EFF6FF; border-radius: 12px; padding: 2px; position: relative; min-width: 200px;">
      <div style="display: flex; flex-direction: column; justify-content: space-between; align-items: flex-start;">
        <div style="flex: 1; min-width: 0;">
          <span style="color: #1B84FF; font-weight:700; font-size:14px; display:block; margin-bottom:2px; overflow:hidden; text-overflow:ellipsis; white-space:nowrap; max-width:180px;">
            ${event.title}
          </span>
        </div>
        <span style="font-weight:500; color: #515151; font-size:11px; display:block; margin-top:2px;">
        ${moment(new Date(event.start)).format('hh:mm A')} To ${moment(
      new Date(event.end)
    ).format('hh:mm A')} / ${moment(new Date(event.start)).format('MMM D YYYY')}
      </span>
      </div>
      <span style="display: flex; align-items: center; padding:3px;">
        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1 6.25V1.75C1 1.33579 1.33579 1 1.75 1H10.75C11.1642 1 11.5 1.33579 11.5 1.75V10.75C11.5 11.1642 11.1642 11.5 10.75 11.5H6.25M3.33333 6.25H6.25M6.25 6.25V9.16667M6.25 6.25L1 11.5" stroke="#1B84FF" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </span>
    </div>`
  },
  timegridDisplayPrimaryTime({ time }) {
    return `<span style="font-weight:500; color: #515151; font-size:14px;">${moment(
      new Date(time)
    ).format('hh A')}</span>`
  },
  weekDayName(model) {
    const date = moment(new Date(model.dateInstance))
    const isToday = date.isSame(moment(), 'day')
    return `
     <span style="font-weight:${isToday ? '700' : '400'}; color: ${
      isToday ? '#1B84FF' : '#515151'
    }; font-size:30px;">${model.date} <span style="font-weight:${
      isToday ? '700' : '500'
    }; color: ${isToday ? '#1B84FF' : '#515151'}; font-size:14px;">${
      model.dayName
    }</span></span>
    `
    // return `<span>${model.date}</span><br/><span>${model.dayName}</span>`;
  },

  timegridDisplayTime({ time }) {
    return `sub timezone: ${time}`
  },
  comingDuration(event) {
    return `<span style="font-weight:500; color: #515151; font-size:14px; ">${event.comingDuration}</span>`
  },
  goingDuration(event) {
    return `<span>${event.goingDuration}</span>`
  },
  // taskTitle() {
  //   return `<span>Task events</span>`;
  // },
  allday(event) {
    return `<span style="color: red;">${event.title}</span>`
  },
}

// Options in Application
export const programOptions = [
  { label: 'Commercial Refrigeration Training (Phoenix)', value: 1 },
  { label: 'Plumbing Technician', value: 2 },
  { label: 'Electrical Technician', value: 3 },
  {
    label: 'Heating, Ventilation and Air Conditioning (HVAC) Technician',
    value: 4,
  },
]
export const locationOptions = [
  { label: 'Nevada', value: 1 },
  { label: 'Texas', value: 2 },
  { label: 'Arizona', value: 3 },
]
export const classTimeOptions = [
  { label: 'Morning', value: 1 },
  { label: 'Afternoon', value: 2 },
  { label: 'Evening', value: 3 },
]
export const instructionMethodOptions = [
  { label: 'In Person', value: 1 },
  { label: 'Hybrid', value: 2 },
  { label: 'Remote', value: 3 },
]
export const cohortOptions = [
  { label: 'July 2025', value: 1 },
  { label: 'August 2025', value: 2 },
  { label: 'September 2025', value: 3 },
  { label: 'October 2025', value: 4 },
  { label: 'November 2025', value: 5 },
  { label: 'December 2025', value: 6 },
]
export const months = [
  { label: '3 Months', value: '1' },
  { label: '6 Months', value: '2' },
  { label: '9 Months', value: '3' },
]
export const aidTypeOptions = [
  { label: 'Ascent Funding', value: 1 },
  { label: 'Veteran Education Benefits', value: 2 },
  { label: 'Vocational Rehab benefits', value: 3 },
]

const LeadGeneration = () => {
  const userProfile = useContext(UserProfileContext)
  const { userID } = useContext(UserProfileContext)
  const { leadDetails, setLeadDetails, fetchLeadDetails } =
    useFetchLeadDetails()
  const { handleTimelineEvent } = useHandleTimelineEvent()
  // const { id, leadDetails } = props;
  const router = useRouter()
  const toast = useRef()
  const [currentTab, setCurrentTab] = useState(0)
  const [modalVisible, setModalVisible] = useState(false)
  const [modalFields, setModalFields] = useState([])
  const [modalTitle, setModalTitle] = useState('')
  const [convertModalVisible, setConvertModalVisible] = useState(false)
  const [appointmentSlot, setAppointmentSlot] = useState(null)
  // const [leadDetails, setLeadDetails] = useState(null);
  const calendarRef = useRef(null)

  const [loadingSection, setLoadingSection] = useState(null)
  const id = router.query.id // Use the id from props if available
  console.log('leadDetails', leadDetails)
  const handleUpdate = async (updatedData, section) => {
    // Update state immediately to prevent flickering
    console.log('updatedData', updatedData)
    setLoadingSection(section)
    setLeadDetails((prev) => ({
      ...prev,
      ...updatedData,
    }))

    try {
      const response = await callApi({
        method: 'PATCH',
        url: `Leads`,
        data: {
          ...updatedData,
          leadId: leadDetails?.id,
        },
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
      })

      // Only update with response data if it's different from what we already set
      if (
        response?.data &&
        JSON.stringify(response.data) !== JSON.stringify(updatedData)
      ) {
        const successMessages = {
          personal: 'Personal information updated successfully.',
          address: 'Address updated successfully.',
          communication: 'Communication preferences updated successfully.',
          agent: 'Assigned agent updated successfully.',
        }

        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: successMessages[section] || 'Details updated successfully.',
          icon: <Image src={success} alt="success" />,
          closeIcon: <Image src={close} alt="close" />,
        })
      }
    } catch (error) {
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to update details.',
        icon: <Image src={reject} alt="error" />,
        closeIcon: <Image src={close} alt="close" />,
      })
      console.error('Error updating lead details:', error)
    } finally {
      setLoadingSection(null)
    }
  }

  useEffect(() => {
    if (leadDetails?.id) {
      fetchLeadDetails()
    }
  }, [leadDetails?.id, currentTab])
  const [calendarTimeline, setCalendarTimeline] = useState([])
  const [unavailableTimeSlots, setUnavailableTimeSlots] = useState([])
  const { token, error } = useGetMeetingToken()
  const { callApi, loading: apiLoading, callApiFetch } = useApi()
  const [loading, setLoading] = useState(false)
  const [calendarView, setCalendarView] = useState(true)
  const [showStudentForm, setShowStudentForm] = useState(false)
  const [studentFormData, setStudentFormData] = useState(null)
  // const [dayCount, setDaycount] = useState(0);

  const { createUserFriendlyDate, createDashboardDate } = useUtilityFunctions()

  const handleBack = () => {
    router.back()
  }

  // console.log("time", startDateTime, endDateTime);
  const today = moment()
  const startOfWeek = today.clone().startOf('week').subtract(2, 'weeks') // Sunday
  const endOfWeek = today.clone().endOf('week').add(2, 'weeks').endOf('day') // Saturday 23:59:59

  const startDateTime = startOfWeek.toISOString()
  const endDateTime = endOfWeek.toISOString()

  useEffect(() => {
    console.log('ref', calendarRef)
    const fetchTeamsMeeting = async () => {
      const ianaZone = Intl.DateTimeFormat().resolvedOptions().timeZone
      const timeZone = ianaToMsTimeZone[ianaZone] || 'UTC'
      const res = await fetch(
        `https://graph.microsoft.com/v1.0/me/calendarView?startDateTime=${startDateTime}&endDateTime=${endDateTime}&top=200`,
        {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
            Prefer: `outlook.timezone="${timeZone}"`, // Optional: for local time
          },
        }
      )

      const data = await res.json()
      console.log('event', data)
      const transformedData = []
      const unavailableTime = []

      if (data?.value?.length) {
        for (let index = 0; index < data.value.length; index++) {
          const item = data.value[index]
          transformedData.push({
            id: index,
            calendarId: 'cal1',
            title: item.subject,
            category: 'time',
            start: item.start.dateTime,
            end: item.end.dateTime,
            customStyle: {
              fontSize: '40px',
              fontWeight: 'bold',
              borderRadius: '10px',
              height: '50px',
            },
          })
          unavailableTime.push({
            startTime: new Date(item.start.dateTime),
            endTime: new Date(item.end.dateTime),
          })
        }
      }

      setCalendarTimeline(transformedData)
      setUnavailableTimeSlots(unavailableTime)
      const int = calendarRef?.current?.getInstance()
      if (int) {
        int.setDate(new Date(startDateTime))
      }
    }

    if (token) {
      fetchTeamsMeeting()
    }
  }, [token, modalVisible])

  const {
    rows: applicationRows,
    setRows: applicationSetRows,
    totalCount: applicationTotalCount,
    setTotalCount: applicationSetTotalCount,
    lazyParams: applicationLazyParams,
    globalFilter: applicationGlobalFilter,
    onSort: applicationOnSort,
    onPage: applicationOnPage,
    onGlobalFilterChange: applicationOnGlobalFilterChange,
    lazyParamsToQueryString: applicationLazyParamsToQueryString,
  } = useDashboard({
    initialLazyParams: {
      first: 0,
      rows: 10,
      sortOrder: -1,
      page: 0,
      filters: {
        global: { value: '', matchMode: 'contains' },
      },
    },
  })

  const queryString = applicationLazyParamsToQueryString(applicationLazyParams)

  useEffect(() => {
    const fetchLeadApplications = async () => {
      try {
        const response = await callApi({
          method: 'GET',
          url: `Leads/LeadsApplication${queryString}&leadId=${leadDetails?.id}`,
        })
        if (response?.data) {
          applicationSetRows(response.data.rows)
          applicationSetTotalCount(response.data.count)
        }
      } catch (error) {
        console.error('Error fetching form definition options:', error)
      }
    }
    if (leadDetails?.id && showStudentForm === false) {
      fetchLeadApplications()
    }
  }, [leadDetails, showStudentForm])

  const convertToStudent = async (notes) => {
    const requestBody = {
      leadId: leadDetails?.id,
      isStudent: true,
      notes: notes,
    }
    try {
      const response = await callApi({
        method: 'PATCH',
        url: `Leads/Notes`,
        data: requestBody,
      })
      if (response?.data) {
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: 'Lead converted to student successfully.',
          icon: <Image src={success} alt="success" />,
          closeIcon: <Image src={close} alt="close" />,
        })
        setConvertModalVisible(false)
        await handleTimelineEvent(
          'Converted to Student',
          leadDetails?.id,
          leadDetails?.leadTimeLines?.[0]?.stagesJson,
          userProfile.displayName,
          notes,
          fetchLeadDetails
        )
        router.back()
      }
    } catch (error) {
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to convert lead to student.',
        icon: <Image src={reject} alt="error" />,
        closeIcon: <Image src={close} alt="close" />,
      })
      console.error('Error fetching form definition options:', error)
    }
  }

  const Header = ({
    currentTab,
    calendarView,
    setCalendarView,
    name,
    isStudent,
  }) => {
    return (
      <div className="flex w-full" style={{ justifyContent: 'space-between' }}>
        <div className="flex align-items-center">
          <Button
            icon={<Image src={Backarrow} alt="Back" />}
            onClick={handleBack}
            className={styles.iconButton}
            style={{ marginRight: '15px' }}
          />
          <Image src={UserProfile} alt="User" />
          <span className={styles.titleName}>
            {`${name} |`}
            <span className="mt-4 text-lg text-gray-600">
              {isStudent ? 'Student' : 'Prospect'}
            </span>
          </span>
          <ConditionalDisplay
            condition={currentTab === 5}
            className={'ml-4 flex items-center gap-2'}
            style={{ marginTop: '0.8rem' }}
          >
            <span style={{ color: '#515151', fontWeight: '600' }}>
              {'Calendar View'}
            </span>
            <InputSwitch
              checked={calendarView}
              onChange={(e) => setCalendarView(e.value)}
              className="custom-lead"
            />
          </ConditionalDisplay>
        </div>
        <ConditionalDisplay condition={!leadDetails?.isStudent}>
          <div>
            <Button
              label="Convert to Student"
              onClick={() => setConvertModalVisible(true)}
              width={'200px'}
              theme="metronic"
              variant="outline"
            />
          </div>
        </ConditionalDisplay>
      </div>
    )
  }

  // const AssignedAgent = () => {
  //   const handleInputChange = () => { };
  //   return (
  //     <div className={clsx("p-0", styles.gridCard)}>
  //       <div className={styles.formTitle}>Assigned Agent</div>
  //       <div className={` ${styles.inputGroup} ${styles.formSide}`}>
  //         <div className="flex w-full gap-4 mt-4">
  //           <TextInput
  //             label="Name"
  //             name="firstName"
  //             value={`Steve Hayden`}
  //             theme="metronic"
  //           />
  //         </div>
  //         <div className="flex w-full gap-4 mt-4">
  //           <TextInput
  //             label="Email"
  //             name="primaryEmail"
  //             value={`<EMAIL>`}
  //             theme="metronic"
  //           />
  //           <TextInput
  //             label="Primary Phone"
  //             name="primaryPhone"
  //             value={"****** 987-1235"}
  //             disabled={true}
  //             onChange={handleInputChange}
  //             theme="metronic"
  //           />
  //         </div>
  //       </div>
  //     </div>
  //   );
  // };

  // Set display name for better debugging and to satisfy ESLint
  Appointments.displayName = 'Appointments'

  // Tasks

  const CompletedTasks = () => {
    const handleInputChange = () => {}

    const mockRows = [
      {
        action: 'ViewEdit',
        subject: 'Email/Call the Contact',
        category: '$Category',
        assignee: 'Steve Hayden',
        completionDate: '$date',
        description: '$description',
      },
      {
        action: 'ViewEdit',
        subject: 'Send Flyer',
        category: '$Category',
        assignee: 'Steve Hayden',
        completionDate: '$date',
        description: '$description',
      },
      {
        action: 'ViewEdit',
        subject: 'Follow Up',
        category: '$Category',
        assignee: 'Steve Hayden',
        completionDate: '$date',
        description: '$description',
      },
      ...Array(7).fill({
        action: 'ViewEdit',
        subject: '$name',
        category: '$Category',
        assignee: '$assignee',
        completionDate: '$date',
        description: '$description',
      }),
    ]

    const actionBodyTemplate = () => (
      <span style={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>
        <span
          className="pi pi-eye"
          style={{ fontSize: '1.2rem', cursor: 'pointer' }}
          onClick={() => {}}
        />
        <span
          className="pi pi-pencil"
          style={{ fontSize: '1.2rem', cursor: 'pointer' }}
          onClick={() => {}}
        />
      </span>
    )
    const tableheader = () => (
      <div className={styles.prospecTable}>
        <span className="text-xl font-bold">Completed Tasks</span>
        <span
          className="p-input-icon-left"
          style={{
            display: 'flex',
            flexDirection: 'row-reverse',
          }}
        >
          <i
            className={clsx('pi pi-search', styles.searchIcon)}
            style={{ top: '22px' }}
          />
          <InputText placeholder="Search" className={styles.search} />
        </span>
      </div>
    )

    const mockColumns = [
      {
        field: 'action',
        header: 'Action',
        body: actionBodyTemplate,
      },
      { field: 'subject', header: 'Subject' },
      { field: 'category', header: 'Category' },
      { field: 'assignee', header: 'Assignee' },
      { field: 'completionDate', header: 'Completion Date' },
      { field: 'description', header: 'Description' },
    ]

    return (
      <div>
        <DataTable
          header={tableheader}
          className="custom-lead"
          value={mockRows}
          paginator
          rows={10}
          style={{ cursor: 'pointer' }}
        >
          {mockColumns.map((col, index) => (
            <Column
              key={index}
              field={col.field}
              header={col.header}
              {...(col.body ? { body: col.body } : {})}
            />
          ))}
        </DataTable>
      </div>
    )
  }

  const RegisteredClasses = () => {
    const actionBodyTemplate = () => (
      <Image src={eye} alt="eye" onClick={() => {}} />
    )
    const tableheader = () => (
      <div className={styles.prospecTable}>
        <span className="text-xl font-bold">Registered Classes</span>
        <div className="flex gap-4">
          <span
            className="p-input-icon-left"
            style={{
              display: 'flex',
              flexDirection: 'row-reverse',
            }}
          >
            <i
              className={clsx('pi pi-search', styles.searchIcon)}
              style={{ top: '22px' }}
            />
            <InputText placeholder="Search" className={styles.search} />
          </span>
        </div>
      </div>
    )
    const mockColumns = [
      { field: 'action', header: 'Action', body: actionBodyTemplate },
      { field: 'name', header: 'Program' },
      { field: 'level', header: 'Course' },
      { field: 'code', header: 'Title' },
      { field: 'status', header: 'Location' },
      { field: 'location', header: 'Instruction Method' },
      { field: 'method', header: 'Term' },
      { field: 'start', header: 'Start Date' },
      { field: 'end', header: 'End Date' },
      { field: 'end', header: 'Final Grade' },
      { field: 'end', header: 'Status' },
    ]

    return (
      <div>
        <DataTable
          header={tableheader}
          className="custom-lead"
          // value={mockRows}
          paginator
          rows={10}
          style={{ cursor: 'pointer' }}
        >
          {mockColumns.map((col, index) => (
            <Column
              key={index}
              field={col.field}
              header={col.header}
              body={col.body}
            />
          ))}
        </DataTable>
      </div>
    )
  }

  // Modified openModal to support appointment modal with react-schedule-meeting
  const openModal = (fields, title, modalType) => {
    if (modalType === 'appointment') {
      setAppointmentSlot(null) // reset slot
      setModalFields(fields)
      setModalTitle(title)
      setModalVisible('appointment')
    } else {
      setModalFields(fields)
      setModalTitle(title)
      setModalVisible(true)
    }
  }

  const closeModal = () => setModalVisible(false)

  // Helper: map columns to modal fields
  const getFieldsFromColumns = (columns, modalType) =>
    columns
      .filter((col) => {
        // For Appointments modal, exclude startTime and endTime fields
        if (modalType === 'appointment') {
          return col.field !== 'startTime' && col.field !== 'endTime'
        }
        return col.field !== 'action'
      })
      .map((col) => ({
        name: col.field,
        label: col.header,
        required: false,
        maxLength: col.field === 'testScore' ? 100 : undefined,
      }))

  const ianaToMsTimeZone = {
    // India
    'Asia/Kolkata': 'India Standard Time',
    'Asia/Calcutta': 'India Standard Time',

    // USA
    'Pacific/Honolulu': 'Hawaiian Standard Time',
    'America/Anchorage': 'Alaskan Standard Time',
    'America/Los_Angeles': 'Pacific Standard Time',
    'America/Denver': 'Mountain Standard Time',
    'America/Phoenix': 'US Mountain Standard Time',
    'America/Chicago': 'Central Standard Time',
    'America/Indiana/Indianapolis': 'US Eastern Standard Time',
    'America/New_York': 'Eastern Standard Time',
  }

  async function createTeamsMeeting(token, subject, startDateTime) {
    // Convert native JS Date to a moment object
    const start = moment(startDateTime)

    // Clone and add 60 minutes
    const end = start.clone().add(60, 'minutes')

    // Format to ISO strings
    const startDateTimeISO = start.toISOString()
    const endDateTime = end.toISOString()

    const ianaZone = Intl.DateTimeFormat().resolvedOptions().timeZone
    const timeZone = ianaToMsTimeZone[ianaZone] || 'UTC'

    const attendees = [
      {
        emailAddress: {
          address: leadDetails?.primaryEmail,
          name: leadDetails?.firstName + leadDetails?.lastName,
        },
        type: 'required',
      },
    ]

    // const res = await fetch("/form-builder-studio/api/graph", {
    //   method: "POST",
    //   headers: { "Content-Type": "application/json" },
    //   body: JSON.stringify({
    //     accessToken: token,
    //     subject: subject,
    //     attendees: [
    //       {
    //         emailAddress: {
    //           address: leadDetails?.primaryEmail,
    //           name: leadDetails?.firstName + leadDetails?.lastName,
    //         },
    //         type: "required",
    //       },
    //     ],
    //     startDateTime: startDateTimeISO,
    //     endDateTime: endDateTime,
    //     timeZone: msTimeZone,
    //   }),
    // });

    const event = {
      subject: subject || '',
      body: {
        contentType: 'HTML',
        content: '',
      },
      start: {
        dateTime: startDateTimeISO,
        timeZone: timeZone,
      },
      end: {
        dateTime: endDateTime,
        timeZone: timeZone,
      },
      location: {
        displayName: '',
      },
      attendees: attendees,
      allowNewTimeProposals: true,
      transactionId: Guid.newGuid().StringGuid,
      isOnlineMeeting: true,
      onlineMeetingProvider: 'teamsForBusiness',
    }

    const graphRes = await fetch('https://graph.microsoft.com/v1.0/me/events', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(event),
    })

    if (graphRes.ok) {
      const requestBody = {
        leadId: 0,
        type: 0,
        title: 'string',
        startTime: '2025-05-12T11:26:36.566Z',
        endTime: '2025-05-12T11:26:36.566Z',
        assignee: 'string',
        link: 'string',
        bookingStatus: 0,
      }
      toast.current.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Create Successfully!',
        icon: <Image src={success} alt="success" />,
        closeIcon: <Image src={close} alt="close" />,
      })
      try {
        // const response = await callApi({
        //   method: "POST",
        //   url: `Leads/Appointments`,
        //   data: requestBody,
        // });
        // if (response?.data) {
        // }
      } catch (error) {
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to create.',
          icon: <Image src={reject} alt="error" />,
          closeIcon: <Image src={close} alt="close" />,
        })
        console.error('Error fetching form definition options:', error)
      }
    }
  }

  const [studentAppModalVisible, setStudentAppModalVisible] = useState(false)

  const createApplicationForm = async (
    data,
    leadDetails,
    isStudentEditable,
    createDashboardDate,
    userID
  ) => {
    const formData = new FormData()

    formData.append('leadId', leadDetails?.id)
    formData.append('programId', data.program)
    formData.append('createdById', userID)
    formData.append('location', data.location)
    formData.append('classTime', data.classTime)
    formData.append('instructionMethod', data.instructionMethod)
    formData.append('desiredTerm', 0)
    formData.append('duration', data.duration)
    if (data.dob && typeof data.dob === 'object') {
      formData.append('leadDOB', createDashboardDate(data.dob.toISOString()))
    } else {
      formData.append('leadDOB', data.dob)
    }
    formData.append('status', isStudentEditable ? 2 : 1)
    formData.append('cohort', data.cohort)
    formData.append('feePaid', false)
    formData.append('financialAidAvailable', data.aidAvailable)
    formData.append('financialAidType', data.aidType)
    formData.append('isStudentEditable', isStudentEditable)
    if (data.startDate?.trim() !== '') {
      formData.append('startDate', data.startDate)
    }
    if (data.endDate?.trim() !== '') {
      formData.append('endDate', data.endDate)
    }

    data?.aidFiles?.map((file, index) => {
      formData.append(`attachments[${index}].fileType`, file.type)
      formData.append(`attachments[${index}].fileName`, file.name)
      formData.append(`attachments[${index}].fileSize`, file.size)
      formData.append(`attachments[${index}].file`, file)
      formData.append(`attachment[${index}].isDocument`, false)
    })

    const accessToken = await getAccessTokenForScopeSilent(
      formBuilderApiRequest
    )

    try {
      const fetchParams = {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        body: formData,
      }
      const response = await callApiFetch(
        `${api}Leads/LeadsApplication`,
        fetchParams
      )
      if (response) {
        const result = await callApi({
          method: 'PATCH',
          url: 'LeadsWorkflow',
          data: {
            leadId: leadDetails?.id,
            stage: 9,
          },
        })
        handleTimelineEvent(
          'Application submitted',
          leadDetails?.id,
          leadDetails?.leadTimeLines?.[0]?.stagesJson,
          userProfile.displayName,
          null,
          fetchLeadDetails
        ) // TODO: Update null to message body
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: isStudentEditable
            ? 'Application sent successfully'
            : 'Application Form created successfully.',
          icon: <Image src={success} alt="success" />,
          closeIcon: <Image src={close} alt="close" />,
        })
        if (!isStudentEditable) {
          setShowStudentForm(response.id)
        }
      }
    } catch (error) {
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to create.',
        icon: <Image src={reject} alt="error" />,
        closeIcon: <Image src={close} alt="close" />,
      })
      console.error('Error fetching lead details:', error)
    }
  }

  return (
    <>
      <PageContainer theme="metronic">
        <Toast ref={toast} className="custom-lead"></Toast>
        <Header
          currentTab={currentTab}
          calendarView={calendarView}
          setCalendarView={setCalendarView}
          name={
            leadDetails?.firstName && leadDetails?.lastName
              ? `${leadDetails.firstName} ${leadDetails.lastName}`
              : 'Name'
          }
          isStudent={leadDetails?.isStudent}
        />
        <div className="flex mt-4 gap-3">
          <div className={styles.tabContainer}>
            <div style={{ display: 'flex', gap: '1.875rem', height: '2.5rem' }}>
              <Tab
                value={0}
                title={`Basic Info`}
                display={true}
                isActive={currentTab === 0}
                handleClick={(e, value) => setCurrentTab(value)}
                theme="metronic"
              />
              <Tab
                value={1}
                title={`Prospect Stage`}
                display={true}
                isActive={currentTab === 1}
                handleClick={(e, value) => setCurrentTab(value)}
                theme="metronic"
              />

              <Tab
                value={2}
                title={`Education History`}
                display={true}
                isActive={currentTab === 2}
                handleClick={(e, value) => setCurrentTab(value)}
                theme="metronic"
              />
              <Tab
                value={4}
                title={`Engagement Log`}
                display={true}
                isActive={currentTab === 4}
                handleClick={(e, value) => setCurrentTab(value)}
                theme="metronic"
              />
              <Tab
                value={3}
                title={`Tasks`}
                display={true}
                isActive={currentTab === 3}
                handleClick={(e, value) => setCurrentTab(value)}
                theme="metronic"
              />
              <Tab
                value={5}
                title={`Appointments`}
                display={true}
                isActive={currentTab === 5}
                handleClick={(e, value) => setCurrentTab(value)}
                theme="metronic"
              />

              <Tab
                value={6}
                title={`Applications`}
                display={true}
                isActive={currentTab === 6}
                handleClick={(e, value) => setCurrentTab(value)}
                theme="metronic"
              />
              <Tab
                value={7}
                title={`Programs`}
                display={leadDetails?.isStudent}
                isActive={currentTab === 7}
                handleClick={(e, value) => setCurrentTab(value)}
                theme="metronic"
              />
              <Tab
                value={8}
                title={`Audit History`}
                display={true}
                isActive={currentTab === 8}
                handleClick={(e, value) => setCurrentTab(value)}
                theme="metronic"
              />
              <Tab
                value={9}
                title={`Audit History`}
                display={true}
                isActive={currentTab === 9}
                handleClick={(e, value) => setCurrentTab(value)}
                theme="metronic"
              />
            </div>
          </div>
        </div>
        <ConditionalDisplay condition={currentTab === 0}>
          <BasicInfo
            leadDetails={leadDetails}
            setLeadDetails={setLeadDetails}
            fetchLeadDetails={fetchLeadDetails}
            handleUpdate={handleUpdate}
            loadingSection={loadingSection}
            parseHtml={parseHtml}
          />
        </ConditionalDisplay>
        <ConditionalDisplay condition={currentTab === 1}>
          <LeadGenerationWorkflow
            leadsWorkflow={leadDetails?.leadsWorkflow}
            leadId={leadDetails?.id}
            fetchLeadDetails={fetchLeadDetails}
          />
        </ConditionalDisplay>
        <ConditionalDisplay condition={currentTab === 2}>
          <Education
            openModal={openModal}
            getFieldsFromColumns={getFieldsFromColumns}
          />
        </ConditionalDisplay>
        <ConditionalDisplay condition={currentTab === 3}>
          <PendingTasks
            isStudent={leadDetails?.isStudent}
            assignee={leadDetails?.agentName}
            openModal={openModal}
            getFieldsFromColumns={getFieldsFromColumns}
          />
          {/* <div className="mt-3"></div> */}
          {/* <CompletedTasks /> */}
        </ConditionalDisplay>
        <ConditionalDisplay condition={currentTab === 4}>
          <EngagementLog
            openModal={openModal}
            getFieldsFromColumns={getFieldsFromColumns}
            leadDetails={leadDetails}
          />
        </ConditionalDisplay>
        <ConditionalDisplay condition={currentTab === 5}>
          <Appointments
            calendarTimeline={calendarTimeline}
            calendarView={calendarView}
            assignee={leadDetails?.agentName}
            ref={calendarRef}
            template={template}
            openModal={openModal}
            getFieldsFromColumns={getFieldsFromColumns}
            modalVisible={modalVisible}
          />
        </ConditionalDisplay>
        <ConditionalDisplay condition={currentTab === 6}>
          <Applications
            lazyParams={applicationLazyParams}
            onPage={applicationOnPage}
            onSort={applicationOnSort}
            totalCount={applicationTotalCount}
            loading={apiLoading?.['Leads/LeadsApplication']}
            globalFilter={applicationGlobalFilter}
            onGlobalFilterChange={applicationOnGlobalFilterChange}
            rows={applicationRows}
            setShowStudentForm={setShowStudentForm}
            showStudentForm={showStudentForm}
            apiLoading={apiLoading}
            leadDetails={leadDetails}
            setStudentAppModalVisible={setStudentAppModalVisible}
            createApplicationForm={createApplicationForm}
          />
        </ConditionalDisplay>
        <ConditionalDisplay condition={currentTab === 7}>
          <Programs />
          <RegisteredClasses />
        </ConditionalDisplay>
        <ConditionalDisplay condition={currentTab === 8}>
          <AuditHistory
            assignee={leadDetails?.agentName}
            openModal={openModal}
            getFieldsFromColumns={getFieldsFromColumns}
          />
        </ConditionalDisplay>
      </PageContainer>
      {/* Default modal for all except appointment */}
      {modalVisible === true && (
        <LeadGenerationModal
          visible={modalVisible}
          onHide={closeModal}
          fields={modalFields}
          title={modalTitle}
          onSubmit={(data) => {
            closeModal()
          }}
        />
      )}
      {/* Custom modal for appointment with react-schedule-meeting */}
      {modalVisible === 'appointment' && (
        <LeadGenerationModal
          calendarTimeline={calendarTimeline}
          unavailableTimeSlots={unavailableTimeSlots}
          visible={true}
          onHide={closeModal}
          fields={modalFields}
          title={modalTitle}
          loading={loading}
          leadName={`${leadDetails?.firstName} ${leadDetails?.lastName}`}
          onSubmit={async (data) => {
            // Build event object using selected slot and form data
            setLoading(true)
            const moment = require('moment') // if in Node.js

            const hasExactMatch = calendarTimeline.some((date) =>
              moment(data.appointmentSlot.startTime).isSame(moment(date.start))
            )

            if (data.appointmentSlot) {
              if (hasExactMatch) {
                setLoading(false)
                alert('Meeting cannot be scheduled because of conflicts')
              } else {
                await createTeamsMeeting(
                  token,
                  data.title,
                  data.appointmentSlot.startTime
                )
                // Track appointment creation in timeline
                const result = await handleTimelineEvent(
                  'Appointment Created',
                  leadDetails?.id,
                  leadDetails?.leadTimeLines?.[0]?.stagesJson,
                  userProfile.displayName,
                  formatAppointment(data),
                  fetchLeadDetails
                )
                await callApi({
                  method: 'PATCH',
                  url: 'LeadsWorkflow',
                  data: {
                    leadId: leadDetails?.id,
                    stage: 5,
                  },
                })
                if (result) {
                }

                // Refresh UI (removed fetchLeadDetails here)
                closeModal()
                setLoading(false)
              }
            }
          }}
        />
      )}
      <ConvertModal
        visible={convertModalVisible}
        onHide={() => setConvertModalVisible(false)}
        onSubmit={(notes) => convertToStudent(notes)}
        isLoading={apiLoading?.['Leads/Notes']}
      />
      <StudentApplicationModal
        leadDetails={leadDetails}
        visible={studentAppModalVisible}
        onHide={() => setStudentAppModalVisible(false)}
        onSubmit={(data) => {
          createApplicationForm(
            data,
            leadDetails,
            false,
            createDashboardDate,
            userID
          )
          setStudentFormData(data)
          setStudentAppModalVisible(false)
        }}
        isLoading={apiLoading?.['Leads/LeadsApplication']}
      />
    </>
  )
}

export default LeadGeneration

export const StudentApplicationForm = ({
  applicationId,
  setShowStudentForm,
  leadId,
}) => {
  // Mock data for demonstration
  const { callApi, loading: apiLoading, callApiFetch } = useApi()
  const { createUserFriendlyDate, toDateObject, createDashboardDate } =
    useUtilityFunctions()
  const [application, setApplication] = useState()

  const [dob, setDob] = useState('')
  const [program, setProgram] = useState([])
  const [location, setLocation] = useState([])
  const [classTime, setClassTime] = useState([])
  const [instructionMethod, setInstructionMethod] = useState([])
  const [cohort, setCohort] = useState([])
  const [duration, setDuration] = useState([])

  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')
  const [aidType, setAidType] = useState(0)
  const [aidFiles, setAidFiles] = useState([])
  const [documents, setDocuments] = useState([])

  const router = useRouter()
  const toast = useRef()

  const fileInputRef = useRef()
  const handleFileUpload = (e) => {
    const files = Array.from(e.target.files)
    setAidFiles((prev) => [...prev, ...files])
    e.target.value = null
  }

  // useEffect(() => {
  //   console.log("dob1", dob);
  // }, [dob]);

  const handleDeleteFile = async (idx) => {
    const fileId = aidFiles?.[idx]?.id
    if (fileId) {
      const response = await callApi({
        method: 'DELETE',
        url: `Leads/LeadsApplication/Attachment/${fileId}`,
      })
      if (response.data) {
        setAidFiles((prev) => prev.filter((_, i) => i !== idx))
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: 'File deleted successfully.',
          icon: <Image src={success} alt="success" />,
          closeIcon: <Image src={close} alt="close" />,
        })
      }
    } else {
      setAidFiles((prev) => prev.filter((_, i) => i !== idx))
      toast.current.show({
        severity: 'success',
        summary: 'Success',
        detail: 'File deleted successfully.',
        icon: <Image src={success} alt="success" />,
        closeIcon: <Image src={close} alt="close" />,
      })
    }
  }

  const handleDownloadFile = async (i, isDocument) => {
    const fileId = isDocument ? documents[i]?.fileId : aidFiles[i]?.fileId
    const fileName = isDocument ? documents[i]?.fileName : aidFiles[i]?.fileName
    const fileType = isDocument ? documents[i]?.fileType : aidFiles[i]?.fileType
    try {
      const response = await callApi({
        method: 'GET',
        url: `Leads/LeadsApplication/Download/base64/${fileId}`,
      })
      if (response.data) {
        downloadBase64File(response.data.file, fileName, fileType)
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: 'File downloaded successfully!',
          icon: <Image src={success} alt="success" />,
          closeIcon: <Image src={close} alt="close" />,
        })
      }
    } catch (error) {
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'File downloaded failed!.',
        icon: <Image src={reject} alt="success" />,
        closeIcon: <Image src={close} alt="close" />,
      })
      console.error('Error fetching lead details:', error)
    }
  }

  let today = new Date()

  const mock = {
    applicationId: 'App123456',
    createdBy: 'Registrar_name',
    creationDate: '05/12/2025',
    lastUpdated: '05/12/2025',
    searchId: 'Search_id',
    firstName: 'John',
    lastName: 'Smith',
    email: '<EMAIL>',
    dob: '01/01/2000',
    program: 'HVAC Technician',
    location: 'Sacramento',
    classTime: 'Morning',
    instructionMethod: 'In Person',
    cohort: 'Fall 2025',
    duration: '3 months',
    startDate: '06/02/2025',
    endDate: '09/02/2025',
    tuition: '$7,795.00',
    financialAid: '($3,000.00)',
    totalCost: '$4,795.00',
    aidType: 'Type A',
    aidFiles: [
      'file_1, 5/09/2025',
      'file_2, 5/10/2025',
      'file_3, 5/11/2025',
      'file_4, 5/12/2025',
    ],
    studentTask: 'Signature/Completed',
    feePayment: 'Completed',
    contribution: '$1000',
    lastTaskDate: '05/12/2025',
  }

  useEffect(() => {
    const fetchApplicationById = async () => {
      try {
        const response = await callApi({
          method: 'GET',
          url: `Leads/LeadsApplication/${applicationId}`,
        })
        if (response.data) {
          setApplication(response.data)
        }
      } catch (error) {
        console.error('Error fetching lead details:', error)
      }
    }
    if (applicationId) {
      fetchApplicationById()
    }
  }, [applicationId])

  useEffect(() => {
    if (application) {
      if (
        application?.applicationDTO?.isStudentEditable === false ||
        (application?.applicationDTO?.isStudentEditable &&
          application?.applicationDTO?.status !== 2)
      ) {
        if (application?.leadsDto?.dob?.trim() !== '') {
          setDob(toDateObject(application?.leadsDto?.dob))
        }
      }
      if (
        (application?.applicationDTO?.isStudentEditable &&
          application?.applicationDTO?.status === 5) ||
        (application?.applicationDTO?.isStudentEditable &&
          application?.applicationDTO?.status === 3) ||
        application?.applicationDTO?.isStudentEditable === false
      ) {
        setProgram(application?.applicationDTO?.programId)
      }

      setLocation(application?.applicationDTO?.location)
      setClassTime(application?.applicationDTO?.classTime)
      setInstructionMethod(application?.applicationDTO?.instructionMethod)
      setDuration(application?.applicationDTO?.duration)
      setCohort(application?.applicationDTO?.cohort)
      setStartDate(application?.applicationDTO?.startDate)
      setEndDate(application?.applicationDTO?.endDate)
      setAidType(application?.applicationDTO?.financialAidType)
      setAidFiles(
        application?.applicationDTO?.attachments
          ?.filter((item) => item.isDocument === false)
          ?.map((item) => {
            return { ...item, name: item.fileName }
          })
      )

      setDocuments(
        application?.applicationDTO?.attachments
          ?.filter((item) => item.isDocument === true)
          ?.map((item) => {
            return { ...item, name: item.fileName }
          })
      )
    }
  }, [application])

  useEffect(() => {
    if (cohort && duration) {
      if (cohort === 1) {
        if (duration === '1') {
          setStartDate('07/01/2025')
          setEndDate('09/30/2025')
        } else if (duration === '2') {
          setStartDate('07/01/2025')
          setEndDate('12/31/2025')
        } else if (duration === '3') {
          setStartDate('07/01/2025')
          setEndDate('03/31/2026')
        }
      } else if (cohort === 2) {
        if (duration === '1') {
          setStartDate('08/01/2025')
          setEndDate('10/31/2025')
        } else if (duration === '2') {
          setStartDate('08/01/2025')
          setEndDate('01/31/2026')
        } else if (duration === '3') {
          setStartDate('08/01/2025')
          setEndDate('04/30/2026')
        }
      } else if (cohort === 3) {
        if (duration === '1') {
          setStartDate('09/01/2025')
          setEndDate('12/31/2025')
        } else if (duration === '2') {
          setStartDate('09/01/2025')
          setEndDate('02/28/2026')
        } else if (duration === '3') {
          setStartDate('09/01/2025')
          setEndDate('04/30/2026')
        }
      } else if (cohort === 4) {
        if (duration === '1') {
          setStartDate('10/01/2025')
          setEndDate('09/30/2025')
        } else if (duration === '2') {
          setStartDate('10/01/2025')
          setEndDate('12/31/2025')
        } else if (duration === '3') {
          setStartDate('10/01/2025')
          setEndDate('03/31/2026')
        }
      } else if (cohort === 5) {
        if (duration === '1') {
          setStartDate('11/01/2025')
          setEndDate('01/31/2026')
        } else if (duration === '2') {
          setStartDate('11/01/2025')
          setEndDate('04/30/2026')
        } else if (duration === '3') {
          setStartDate('11/01/2025')
          setEndDate('06/30/2026')
        }
      } else if (cohort === 6) {
        if (duration === '1') {
          setStartDate('12/01/2025')
          setEndDate('02/28/2026')
        } else if (duration === '2') {
          setStartDate('12/01/2025')
          setEndDate('05/31/2026')
        } else if (duration === '3') {
          setStartDate('12/01/2025')
          setEndDate('07/31/2026')
        }
      }
    }
  }, [cohort, duration])

  const handleSave = async (status) => {
    const formData = new FormData()
    formData.append('applicationId', application?.applicationDTO?.id)
    formData.append('leadId', leadId)
    if (
      application?.applicationDTO?.isStudentEditable &&
      application?.applicationDTO?.feePaid === false
    ) {
      formData.append('programId', application?.applicationDTO?.programId)
    } else {
      formData.append('programId', program)
    }

    formData.append('location', location)
    formData.append('classTime', classTime)
    formData.append('instructionMethod', instructionMethod)
    formData.append('desiredTerm', 0)
    formData.append('duration', duration)
    if (dob) {
      formData.append('leadDOB', createDashboardDate(dob.toISOString()))
    }
    formData.append('status', status)
    if (cohort) {
      formData.append('cohort', cohort)
    }
    formData.append('feePaid', false)
    formData.append(
      'financialAidAvailable',
      application.applicationDTO.financialAidAvailable
    )
    formData.append('financialAidType', aidType)
    formData.append(
      'isStudentEditable',
      application.applicationDTO.isStudentEditable
    )
    if (startDate?.trim() !== '' && startDate !== null) {
      formData.append('startDate', startDate)
    }
    if (endDate?.trim() !== '' && startDate !== null) {
      formData.append('endDate', endDate)
    }

    aidFiles
      ?.filter(
        (file) => file.id === 0 || file.id === null || file.id === undefined
      )
      ?.map((file, index) => {
        formData.append(`attachments[${index}].fileType`, file.type)
        formData.append(`attachments[${index}].fileName`, file.name)
        formData.append(`attachments[${index}].fileSize`, file.size)
        formData.append(`attachments[${index}].file`, file)
        formData.append(`attachment[${index}].isDocument`, false)
      })

    const accessToken = await getAccessTokenForScopeSilent(
      formBuilderApiRequest
    )

    try {
      const fetchParams = {
        method: 'PATCH',
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        body: formData,
      }
      const response = await callApiFetch(
        `${api}Leads/LeadsApplication`,
        fetchParams
      )
      if (response) {
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: 'Application Sent successfully.',
          icon: <Image src={success} alt="success" />,
          closeIcon: <Image src={close} alt="close" />,
        })
        setShowStudentForm(false)
      }
    } catch (error) {
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to send application.',
        icon: <Image src={reject} alt="error" />,
        closeIcon: <Image src={close} alt="close" />,
      })
      console.error('Error fetching lead details:', error)
    }
  }

  return (
    <>
      {apiLoading && (
        <ProgressBar
          mode="indeterminate"
          style={{ height: '6px' }}
        ></ProgressBar>
      )}
      <div
        className="mt-4 w-full flex-column flex justify-content-center"
        style={{ background: '#1b84ff03' }}
      >
        <div
          className="mx-auto"
          style={{
            maxWidth: 1400,
            width: '100%',
            background: '#fff',
            borderRadius: 20,
            height: 'auto',
            margin: '30px',
            border: '1px solid #5151511a',
          }}
        >
          <Toast ref={toast} className="custom-lead"></Toast>

          <div className={clsx('mt-0 text-center', styles.formTitle)}>
            Student Application
          </div>
          <div
            className="flex justify-content-between mt-4"
            style={{ marginLeft: '5rem', marginRight: '5rem' }}
          >
            <Button
              icon={<Image src={Backarrow} alt="Back" />}
              onClick={() => setShowStudentForm(false)}
              className={styles.iconButton}
              style={{ marginRight: '15px' }}
            />

            {!apiLoading && (
              <div
                className={clsx(
                  'flex align-items-center gap-3 p-2  justify-content-center',
                  application?.applicationDTO?.status === 2 ||
                    application?.applicationDTO?.status === 4
                    ? styles.formProgress
                    : application?.applicationDTO?.status === 1 ||
                      application?.applicationDTO?.status === 3
                    ? styles.formInfo
                    : styles.formSuccess
                )}
              >
                {application?.applicationDTO?.status === 5 ? (
                  <Image src={success} alt="status" width={22} height={22} />
                ) : (
                  <span
                    className={
                      application?.applicationDTO?.status === 2 ||
                      application?.applicationDTO?.status === 4
                        ? styles.inprogressCircle
                        : application?.applicationDTO?.status === 1 ||
                          application?.applicationDTO?.status === 3
                        ? styles.infoCircle
                        : ''
                    }
                  ></span>
                )}

                <span
                  style={{ fontWeight: 600, fontSize: 14, color: '#515151' }}
                >
                  {application?.applicationDTO?.status === 1
                    ? 'Draft'
                    : application?.applicationDTO?.status === 2
                    ? 'Application Sent'
                    : application?.applicationDTO?.status === 3
                    ? 'In Review'
                    : application?.applicationDTO?.status === 4
                    ? 'Awaiting Acknowledgement & Payment'
                    : application?.applicationDTO?.status === 5
                    ? 'Student Onboarded'
                    : ''}
                </span>
              </div>
            )}
          </div>

          {/* Personal Information */}
          <div className={clsx('mx-8', styles.formGrid)}>
            <div className={clsx('font-bold text-lg mb-4', styles.fontText)}>
              Personal Information
            </div>
            <div className="flex mb-4 gap-4 ">
              <TextInput
                label="Application ID"
                value={
                  application?.applicationDTO?.id
                    ? `APP-${application?.applicationDTO?.id}`
                    : ''
                }
                disabled
                style={{ flex: 1 }}
                theme="metronic"
              />
              <TextInput
                label="Created By"
                value={
                  application?.applicationDTO?.createdBy?.displayName || ''
                }
                disabled
                style={{ flex: 1 }}
                theme="metronic"
              />
            </div>
            <div className="flex mb-4 gap-4 ">
              <TextInput
                label="Creation Date"
                value={createUserFriendlyDate(
                  application?.applicationDTO?.createdAt
                )}
                disabled
                style={{ flex: 1 }}
                theme="metronic"
              />
              <TextInput
                label="Last Updated"
                value={
                  application?.applicationDTO?.updatedAt
                    ? createUserFriendlyDate(
                        application?.applicationDTO?.updatedAt
                      )
                    : createUserFriendlyDate(
                        application?.applicationDTO?.createdAt
                      )
                }
                disabled
                style={{ flex: 1 }}
                theme="metronic"
              />
            </div>
            <div className="flex mb-4 gap-4 ">
              <TextInput
                label="First Name"
                value={application?.leadsDto?.firstName}
                disabled
                style={{ flex: 1 }}
                theme="metronic"
              />
              <TextInput
                label="Last Name"
                value={application?.leadsDto?.lastName}
                disabled
                style={{ flex: 1 }}
                theme="metronic"
              />
            </div>
            <div className="flex mb-4 gap-4 ">
              <TextInput
                label="Email"
                value={application?.leadsDto?.primaryEmail}
                disabled
                style={{ flex: 1 }}
                theme="metronic"
              />

              <TextInput
                label="Date of Birth"
                style={{ flex: 1 }}
                theme="metronic"
                value={dob}
                onChange={(e) => {
                  setDob(e.target.value)
                }}
                calendar={true}
                disabled={
                  application?.applicationDTO?.isStudentEditable ||
                  application?.applicationDTO?.status === 5 ||
                  (!application?.applicationDTO?.isStudentEditable &&
                    application?.applicationDTO?.status === 2)
                }
                placeholder={
                  application?.applicationDTO?.isStudentEditable
                    ? 'To be filled by student'
                    : 'Select a DOB'
                }
                maxDate={today}
              />
            </div>
          </div>

          {/* Program Information */}
          <div className={clsx('mx-8', styles.formGrid)}>
            <div className={clsx('font-bold text-lg mb-4', styles.fontText)}>
              Program Information
            </div>
            <div className="flex mb-4 gap-4 ">
              <SelectInput
                label="Program"
                value={program}
                onChange={(e) => setProgram(e.value)}
                style={{ flex: 1 }}
                theme="metronic"
                height="45px"
                options={programOptions}
                optionLabel="label"
                optionValue="value"
                disabled={
                  application?.applicationDTO?.isStudentEditable ||
                  application?.applicationDTO?.status === 5 ||
                  (!application?.applicationDTO?.isStudentEditable &&
                    application?.applicationDTO?.status === 2)
                }
                placeholder={
                  application?.applicationDTO?.isStudentEditable
                    ? 'To be filled by student'
                    : 'Select a program'
                }
              />
              <SelectInput
                label="Location"
                value={location}
                onChange={(e) => setLocation(e.value)}
                style={{ flex: 1 }}
                theme="metronic"
                height="45px"
                options={locationOptions}
                optionLabel="label"
                optionValue="value"
                disabled={
                  application?.applicationDTO?.isStudentEditable ||
                  application?.applicationDTO?.status === 5 ||
                  (!application?.applicationDTO?.isStudentEditable &&
                    application?.applicationDTO?.status === 2)
                }
                placeholder={
                  application?.applicationDTO?.isStudentEditable
                    ? 'To be filled by student'
                    : 'Select a program'
                }
              />
            </div>
            <div className="flex mb-4 gap-4 ">
              <SelectInput
                label="Class Time"
                value={classTime}
                onChange={(e) => setClassTime(e.value)}
                style={{ flex: 1 }}
                theme="metronic"
                height="45px"
                options={classTimeOptions}
                optionLabel="label"
                optionValue="value"
                disabled={
                  application?.applicationDTO?.isStudentEditable ||
                  application?.applicationDTO?.status === 5 ||
                  (!application?.applicationDTO?.isStudentEditable &&
                    application?.applicationDTO?.status === 2)
                }
                placeholder={
                  application?.applicationDTO?.isStudentEditable
                    ? 'To be filled by student'
                    : 'Select a program'
                }
              />
              <SelectInput
                label="Instruction Method"
                value={instructionMethod}
                onChange={(e) => setInstructionMethod(e.value)}
                style={{ flex: 1 }}
                theme="metronic"
                height="45px"
                options={instructionMethodOptions}
                optionLabel="label"
                optionValue="value"
                disabled={
                  application?.applicationDTO?.isStudentEditable ||
                  application?.applicationDTO?.status === 5 ||
                  (!application?.applicationDTO?.isStudentEditable &&
                    application?.applicationDTO?.status === 2)
                }
                placeholder={
                  application?.applicationDTO?.isStudentEditable
                    ? 'To be filled by student'
                    : 'Select a program'
                }
              />
            </div>
            <div className="flex mb-4 gap-4 ">
              <SelectInput
                label="Cohort"
                value={cohort}
                onChange={(e) => setCohort(e.value)}
                style={{ flex: 1 }}
                theme="metronic"
                height="45px"
                options={cohortOptions}
                optionLabel="label"
                optionValue="value"
                disabled={
                  application?.applicationDTO?.isStudentEditable ||
                  application?.applicationDTO?.status === 5 ||
                  (!application?.applicationDTO?.isStudentEditable &&
                    application?.applicationDTO?.status === 2)
                }
                placeholder={
                  application?.applicationDTO?.isStudentEditable
                    ? 'To be filled by student'
                    : 'Select a program'
                }
              />

              <SelectInput
                label="Duration"
                value={duration}
                height={'45px'}
                onChange={(e) => setDuration(e.value)}
                style={{ flex: 1 }}
                theme="metronic"
                options={months}
                optionLabel="label"
                optionValue="value"
                disabled={
                  application?.applicationDTO?.isStudentEditable ||
                  application?.applicationDTO?.status === 5 ||
                  (!application?.applicationDTO?.isStudentEditable &&
                    application?.applicationDTO?.status === 2)
                }
                placeholder={
                  application?.applicationDTO?.isStudentEditable
                    ? 'To be filled by student'
                    : 'Select a program'
                }
              />
            </div>
            <div className="flex mb-4 gap-4 ">
              <TextInput
                label="Start Date"
                value={startDate}
                style={{ flex: 1 }}
                theme="metronic"
                disabled={
                  application?.applicationDTO?.isStudentEditable ||
                  application?.applicationDTO?.status === 5 ||
                  (!application?.applicationDTO?.isStudentEditable &&
                    application?.applicationDTO?.status === 2)
                }
                placeholder={
                  application?.applicationDTO?.isStudentEditable
                    ? 'To be filled by student'
                    : 'Select a program'
                }
              />
              <TextInput
                label="End Date"
                value={endDate}
                style={{ flex: 1 }}
                theme="metronic"
                disabled={
                  application?.applicationDTO?.isStudentEditable ||
                  application?.applicationDTO?.status === 5 ||
                  (!application?.applicationDTO?.isStudentEditable &&
                    application?.applicationDTO?.status === 2)
                }
                placeholder={
                  application?.applicationDTO?.isStudentEditable
                    ? 'To be filled by student'
                    : 'Select a program'
                }
              />
            </div>
          </div>

          {/* Financial Aid */}
          <ConditionalDisplay
            condition={
              (application?.applicationDTO?.isStudentEditable &&
                application?.applicationDTO?.status !== 2) ||
              (!application?.applicationDTO?.isStudentEditable &&
                application?.applicationDTO?.financialAidAvailable)
            }
          >
            <div className={clsx('mx-8', styles.formGrid)}>
              <div className={clsx('font-bold text-lg mb-4', styles.fontText)}>
                Financial Aid
              </div>
              <div className="flex mb-4 gap-4 ">
                <TextInput
                  label="Amount"
                  value={aidType === 0 ? '$0' : mock.financialAid}
                  disabled
                  style={{ flex: 1 }}
                  theme="metronic"
                />
                <SelectInput
                  label="Type"
                  value={aidType}
                  onChange={(e) => setAidType(e.value)}
                  style={{ flex: 1 }}
                  theme="metronic"
                  height="45px"
                  options={aidTypeOptions}
                  optionLabel="label"
                  optionValue="value"
                  disabled={
                    application?.applicationDTO?.status === 5 ||
                    application?.applicationDTO?.status === 4 ||
                    (!application?.applicationDTO?.isStudentEditable &&
                      application?.applicationDTO?.status === 2)
                  }
                  placeholder={'select an aid type'}
                />
              </div>
              <div style={{ marginBottom: 16 }}>
                <div style={{ fontWeight: 500, marginBottom: 8 }}>
                  Aid Attachments
                </div>
                <div
                  style={{
                    margin: 0,
                    paddingLeft: 0,
                    display: 'flex',
                    flexWrap: 'wrap',
                  }}
                >
                  {aidFiles?.map((file, i) => (
                    <div
                      key={i}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        marginBottom: 10,

                        border: '1px solid #1B84FF',
                        borderRadius: 10,
                        padding: '6px',
                        fontSize: 15,
                        width: 'calc(50% - 8px)',
                        marginRight: i % 2 === 0 ? '16px' : '0',
                        color: '#1B84FF',
                      }}
                    >
                      <span style={{ flex: 1 }}>{file.name}</span>
                      <ConditionalDisplay
                        condition={application?.applicationDTO?.status !== 5}
                      >
                        <ConditionalDisplay
                          condition={
                            (application?.applicationDTO?.status !== 2 &&
                              application?.applicationDTO?.isStudentEditable ===
                                false) ||
                            application?.applicationDTO?.isStudentEditable
                          }
                        >
                          <Image
                            className="cursor-pointer mr-2"
                            src={deleteIcon}
                            alt="delete"
                            onClick={() => handleDeleteFile(i)}
                          />
                        </ConditionalDisplay>
                      </ConditionalDisplay>
                      <ConditionalDisplay
                        condition={
                          application?.applicationDTO?.status === 5 ||
                          (application?.applicationDTO?.status === 3 &&
                            application?.applicationDTO?.isStudentEditable)
                        }
                      >
                        <Image
                          src={download}
                          alt="download"
                          className="cursor-pointer"
                          onClick={() => handleDownloadFile(i, false)}
                        />
                      </ConditionalDisplay>
                    </div>
                  ))}
                  {aidFiles?.length === 0 && (
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        marginBottom: 10,
                        border: '1px solid #1B84FF',
                        borderRadius: 10,
                        padding: '6px',
                        fontSize: 15,
                        width: '50%',
                        color: '#1B84FF',
                      }}
                    >
                      <span style={{ flex: 1 }}>No files attached</span>
                    </div>
                  )}
                </div>
                <ConditionalDisplay
                  condition={
                    (application?.applicationDTO?.isStudentEditable &&
                      application?.applicationDTO?.status === 3) ||
                    (!application?.applicationDTO?.isStudentEditable &&
                      application?.applicationDTO?.status === 1)
                  }
                >
                  <input
                    type="file"
                    multiple
                    ref={fileInputRef}
                    style={{ display: 'none' }}
                    onChange={handleFileUpload}
                  />
                  <Button
                    label="Add files"
                    theme="metronic"
                    style={{ marginTop: 8 }}
                    onClick={() =>
                      fileInputRef.current && fileInputRef.current.click()
                    }
                    variant="outline"
                  />
                </ConditionalDisplay>
              </div>
            </div>
          </ConditionalDisplay>

          {/* Documents */}
          <div className={clsx('mx-8', styles.formGrid)}>
            <div className={clsx('font-bold text-lg mb-4', styles.fontText)}>
              Documents
            </div>
            <div style={{ marginBottom: 16 }}>
              <div style={{ fontWeight: 500, marginBottom: 8 }}>
                Attachments
              </div>
              <div
                style={{
                  margin: 0,
                  paddingLeft: 0,
                  display: 'flex',
                  flexWrap: 'wrap',
                }}
              >
                {documents?.map((file, i) => (
                  <div
                    key={i}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      marginBottom: 10,
                      border: '1px solid #1B84FF',
                      borderRadius: 10,
                      padding: '6px',
                      fontSize: 15,
                      width: 'calc(50% - 8px)',
                      marginRight: i % 2 === 0 ? '16px' : '0',
                      color: '#1B84FF',
                    }}
                  >
                    <span style={{ flex: 1 }}>{file.name}</span>
                    <ConditionalDisplay
                      condition={
                        application?.applicationDTO?.status === 5 ||
                        (application?.applicationDTO?.isStudentEditable &&
                          application?.applicationDTO?.status !== 2)
                      }
                    >
                      <Image
                        src={download}
                        alt="download"
                        className="cursor-pointer"
                        onClick={() => handleDownloadFile(i, true)}
                      />
                    </ConditionalDisplay>
                  </div>
                ))}
                {documents?.length === 0 && (
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      marginBottom: 10,
                      marginTop: 10,
                      background: '#f7fafd',
                      border: '1px solid #1B84FF',
                      borderRadius: 10,
                      padding: '12px',
                      width: '50%',
                      fontSize: 15,
                    }}
                  >
                    <span style={{ flex: 1 }}>
                      No files attached by student
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Student Tasks */}
          <ConditionalDisplay
            condition={
              (application?.applicationDTO?.isStudentEditable &&
                application?.applicationDTO?.status !== 2) ||
              application?.applicationDTO?.isStudentEditable === false
            }
          >
            <div className={clsx('mx-8', styles.formGrid)}>
              <div className={clsx('font-bold text-lg mb-4', styles.fontText)}>
                Student Tasks
              </div>
              <div className="flex mb-4 gap-4 ">
                <TextInput
                  label="Signature"
                  // value={mock.studentTask}
                  value={
                    application?.applicationDTO?.feePaid
                      ? 'Completed'
                      : 'Pending'
                  }
                  disabled
                  style={{ flex: 1 }}
                  theme="metronic"
                />
                <TextInput
                  label="Fee Payment"
                  value={
                    application?.applicationDTO?.feePaid
                      ? 'Completed'
                      : 'Pending'
                  }
                  disabled
                  style={{ flex: 1 }}
                  theme="metronic"
                />
              </div>
              <div className="flex mb-4 gap-4 ">
                <TextInput
                  label="Confirmation request sent on"
                  value={
                    application?.applicationDTO?.status === 2
                      ? createUserFriendlyDate(
                          application?.applicationDTO?.updatedAt
                        )
                      : 'NA'
                  }
                  disabled
                  style={{ flex: 1 }}
                  theme="metronic"
                />
                <TextInput
                  label="Last Updated"
                  value={
                    application?.applicationDTO?.feePaid
                      ? createUserFriendlyDate(
                          application?.applicationDTO?.updatedAt
                        )
                      : 'NA'
                  }
                  disabled
                  style={{ flex: 1 }}
                  theme="metronic"
                />
              </div>
            </div>
          </ConditionalDisplay>

          {/* Tuition and Fees */}
          <ConditionalDisplay
            condition={
              (application?.applicationDTO?.isStudentEditable &&
                application?.applicationDTO?.status !== 2) ||
              application?.applicationDTO?.isStudentEditable === false
            }
          >
            <div className={clsx('mx-8', styles.formGrid)}>
              <div className={clsx('font-bold text-lg mb-4', styles.fontText)}>
                Tuition and Fees
              </div>
              <div className="flex mb-4 gap-4 ">
                <TextInput
                  label="Tuition"
                  value={mock.tuition}
                  disabled
                  style={{ flex: 1 }}
                  theme="metronic"
                />
                <TextInput
                  label="Financial Aid"
                  value={aidType === 0 ? '$0' : mock.financialAid}
                  disabled
                  style={{ flex: 1 }}
                  theme="metronic"
                />
              </div>
              <div
                className={styles.fontText}
                style={{ fontWeight: 700, fontSize: 16, marginTop: 16 }}
              >
                TOTAL COST
              </div>
              <div
                className={styles.fontText}
                style={{ fontWeight: 700, fontSize: 22, marginBottom: 0 }}
              >
                {aidType === 0 ? mock.tuition : mock.totalCost}
              </div>
            </div>
          </ConditionalDisplay>

          {/* Save/Cancel Buttons */}
          <div
            style={{
              display: 'flex',
              gap: 16,
              justifyContent: 'flex-end',
              marginRight: '5rem',
              marginBottom: '2rem',
            }}
          >
            <ConditionalDisplay
              condition={
                application?.applicationDTO?.isStudentEditable === false &&
                application?.applicationDTO?.status === 1
              }
            >
              <Button
                label="Cancel"
                theme="metronic"
                style={{ minWidth: 120, background: '#f4f4f4', color: '#222' }}
                onClick={() => router.back()}
                variant="outline"
              />
              <Button
                label="Send"
                theme="metronic"
                style={{ minWidth: 120 }}
                onClick={() => handleSave(2)}
              />
            </ConditionalDisplay>
            <ConditionalDisplay
              condition={
                application?.applicationDTO?.status === 5 ||
                (application?.applicationDTO?.status === 2 &&
                  application?.applicationDTO?.isStudentEditable) ||
                (application?.applicationDTO?.status === 4 &&
                  application?.applicationDTO?.isStudentEditable) ||
                (application?.applicationDTO?.isStudentEditable === false &&
                  application?.applicationDTO?.status === 2)
              }
            >
              <Button
                label="Back"
                theme="metronic"
                style={{ minWidth: 120, background: '#f4f4f4', color: '#222' }}
                onClick={() => setShowStudentForm(false)}
                variant="outline"
              />
            </ConditionalDisplay>
            <ConditionalDisplay
              condition={
                application?.applicationDTO?.isStudentEditable &&
                application?.applicationDTO?.status === 3
              }
            >
              <Button
                label="Reject"
                theme="metronic"
                style={{ minWidth: 120, background: '#f4f4f4', color: '#222' }}
                // onClick={() => setShowStudentForm(false)}
              />
              <Button
                label="Approve and Initiate Payment"
                theme="metronic"
                style={{ minWidth: 120 }}
                onClick={() => handleSave(4)}
              />
            </ConditionalDisplay>
          </div>
        </div>
      </div>
    </>
  )
}

// --- Student Application Modal (Multi-Step) ---
export const StudentApplicationModal = ({
  leadDetails,
  visible,
  onHide,
  onSubmit,
  isLoading,
}) => {
  const [step, setStep] = useState(1)
  // Step 1 fields
  const [firstName, setFirstName] = useState('')
  const [lastName, setLastName] = useState('')
  const [email, setEmail] = useState('')
  const [dob, setDob] = useState('')
  // Step 2 fields
  const [program, setProgram] = useState(0)
  const [location, setLocation] = useState('')
  const [classTime, setClassTime] = useState(null)
  const [instructionMethod, setInstructionMethod] = useState(null)
  const [cohort, setCohort] = useState(null)
  const [duration, setDuration] = useState([])
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')
  // Step 3 fields
  const [aidAvailable, setAidAvailable] = useState(null)
  const [aidType, setAidType] = useState('')
  const [aidFiles, setAidFiles] = useState([])
  const [documents, setDocuments] = useState([])
  const fileInputRef = useRef()
  const router = useRouter()

  // Add validation state for all steps
  const [validationErrors, setValidationErrors] = useState({
    // Step 1
    dob: false,
    // Step 2
    program: false,
    location: false,
    classTime: false,
    instructionMethod: false,
    cohort: false,
    duration: false,
    // Step 3
    aidAvailable: false,
    aidType: false,
  })

  // Validation functions for each step
  const validateStep1 = () => {
    const errors = {
      ...validationErrors,
      dob: !dob,
    }
    setValidationErrors(errors)
    return !Object.values(errors).some((error) => error)
  }

  const validateStep2 = () => {
    console.log('program', program)
    const errors = {
      ...validationErrors,
      program: !program,
      location: !location,
      classTime: !classTime,
      instructionMethod: !instructionMethod,
      cohort: !cohort,
      duration: !duration,
    }
    setValidationErrors(errors)
    return !Object.values(errors).some((error) => error)
  }

  console.log('validationErrors', validationErrors)

  const validateStep3 = () => {
    const errors = {
      ...validationErrors,
      aidAvailable: aidAvailable === null,
      aidType: aidAvailable && !aidType,
    }
    setValidationErrors(errors)
    return !Object.values(errors).some((error) => error)
  }

  const handleNext = () => {
    let isValid = false
    switch (step) {
      case 1:
        isValid = validateStep1()
        break
      case 2:
        isValid = validateStep2()
        break
      case 3:
        isValid = validateStep3()
        break
      default:
        isValid = true
    }
    if (isValid) {
      setStep(step + 1)
    }
  }

  const handleBack = () => {
    setStep(step - 1)
  }

  useEffect(() => {
    if (leadDetails) {
      setFirstName(leadDetails?.firstName)
      setLastName(leadDetails?.lastName)
      setEmail(leadDetails?.primaryEmail)
      // setProgram(leadDetails?.leadPrograms.map((item) => item.programId));
      // setLocation(leadDetails?.preferredRegion);
      // setInstructionMethod(leadDetails?.instructionMethod?.[0]);
    }
  }, [leadDetails])

  useEffect(() => {
    if (cohort && duration) {
      if (cohort === 1) {
        if (duration === '1') {
          setStartDate('07/01/2025')
          setEndDate('09/30/2025')
        } else if (duration === '2') {
          setStartDate('07/01/2025')
          setEndDate('12/31/2025')
        } else if (duration === '3') {
          setStartDate('07/01/2025')
          setEndDate('03/31/2026')
        }
      } else if (cohort === 2) {
        if (duration === '1') {
          setStartDate('08/01/2025')
          setEndDate('10/31/2025')
        } else if (duration === '2') {
          setStartDate('08/01/2025')
          setEndDate('01/31/2026')
        } else if (duration === '3') {
          setStartDate('08/01/2025')
          setEndDate('04/30/2026')
        }
      } else if (cohort === 3) {
        if (duration === '1') {
          setStartDate('09/01/2025')
          setEndDate('12/31/2025')
        } else if (duration === '2') {
          setStartDate('09/01/2025')
          setEndDate('02/28/2026')
        } else if (duration === '3') {
          setStartDate('09/01/2025')
          setEndDate('04/30/2026')
        }
      } else if (cohort === 4) {
        if (duration === '1') {
          setStartDate('10/01/2025')
          setEndDate('09/30/2025')
        } else if (duration === '2') {
          setStartDate('10/01/2025')
          setEndDate('12/31/2025')
        } else if (duration === '3') {
          setStartDate('10/01/2025')
          setEndDate('03/31/2026')
        }
      } else if (cohort === 5) {
        if (duration === '1') {
          setStartDate('11/01/2025')
          setEndDate('01/31/2026')
        } else if (duration === '2') {
          setStartDate('11/01/2025')
          setEndDate('04/30/2026')
        } else if (duration === '3') {
          setStartDate('11/01/2025')
          setEndDate('06/30/2026')
        }
      } else if (cohort === 6) {
        if (duration === '1') {
          setStartDate('12/01/2025')
          setEndDate('02/28/2026')
        } else if (duration === '2') {
          setStartDate('12/01/2025')
          setEndDate('05/31/2026')
        } else if (duration === '3') {
          setStartDate('12/01/2025')
          setEndDate('07/31/2026')
        }
      }
    }
  }, [cohort, duration])

  const handleFileUpload = (e) => {
    const files = Array.from(e.target.files)
    // const newFiles = files.map(
    //   (file) => file.name + ", " + new Date().toLocaleDateString()
    // );
    setAidFiles((prev) => [...prev, ...files])
    e.target.value = null
  }
  const handleDeleteFile = (idx) => {
    setAidFiles((prev) => prev.filter((_, i) => i !== idx))
  }

  const handleDocumentUpload = (e) => {
    const files = Array.from(e.target.files)
    //   const newFiles = files.map(
    //   (file) => file.name + ", " + new Date().toLocaleDateString()
    // );
    setDocuments((prev) => [...prev, ...files])
    e.target.value = null
  }
  const handleDeleteDocument = (idx) => {
    setDocuments((prev) => prev.filter((_, i) => i !== idx))
  }

  let today = new Date()
  // Modal style
  if (!visible) return null
  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100vw',
        height: '100vh',
        background: '#0008',
        zIndex: 1000,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      <div
        style={{
          width: '40vw',
          background: '#fff',
          borderRadius: 12,
          boxShadow: '0 2px 16px #0002',
          padding: 0,
          position: 'relative',
        }}
      >
        {/* Header */}
        <div
          style={{
            backgroundColor: '#F1F7FD',
            borderTopLeftRadius: '10px',
            borderTopRightRadius: '10px',
            borderBottom: '1px solid #5151511A',
            padding: '20px 32px 12px 32px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <div style={{ fontWeight: 700, fontSize: 20, color: '#515151' }}>
            New Student Application - Step {step}
          </div>
          <Image alt="close" src={closeIcon} onClick={onHide} />
          {/* <button onClick={onHide} style={{ background: 'none', border: 'none', fontSize: 24, cursor: 'pointer', color: '#007aff', fontWeight: 700, lineHeight: 1 }}>&times;</button> */}
        </div>
        {/* Step Content */}
        <div style={{ padding: '24px 32px' }}>
          {step === 1 && (
            <>
              <div className={clsx('font-bold text-lg mb-4', styles.fontText)}>
                Personal Information
              </div>
              <div className="flex flex-column gap-4 ">
                <TextInput
                  label="Student Name"
                  value={'John Smith'}
                  readOnly
                  style={{ flex: 1 }}
                  theme="metronic"
                  required
                />
                <TextInput
                  label="First Name"
                  value={firstName}
                  readOnly
                  style={{ flex: 1 }}
                  theme="metronic"
                  required
                />
                <TextInput
                  label="Last Name"
                  value={lastName}
                  readOnly
                  style={{ flex: 1 }}
                  theme="metronic"
                  required
                />
                <TextInput
                  label="Email"
                  value={email}
                  readOnly
                  style={{ flex: 1 }}
                  theme="metronic"
                  required
                />
                <TextInput
                  label="Date of Birth"
                  style={{ flex: 1 }}
                  theme="metronic"
                  placeholder="DOB"
                  value={dob}
                  onChange={(e) => {
                    setDob(e.target.value)
                    setValidationErrors((prev) => ({ ...prev, dob: false }))
                  }}
                  calendar={true}
                  maxDate={today}
                  required
                  error={validationErrors.dob ? 'This field is required' : ''}
                />
              </div>
              <div
                className="mt-5"
                style={{ display: 'flex', justifyContent: 'flex-end', gap: 8 }}
              >
                <Button
                  label="Next"
                  theme="metronic"
                  style={{ minWidth: 100 }}
                  onClick={handleNext}
                />
              </div>
            </>
          )}
          {step === 2 && (
            <>
              <div className={clsx('font-bold text-lg mb-4', styles.fontText)}>
                Program Information
              </div>
              <div
                className="flex flex-column gap-4 overflow-scroll"
                style={{ height: '35rem' }}
              >
                <SelectInput
                  label="Program"
                  value={program}
                  height={'45px'}
                  onChange={(e) => {
                    setProgram(e.value)
                    setValidationErrors((prev) => ({ ...prev, program: false }))
                  }}
                  style={{ flex: 1 }}
                  theme="metronic"
                  options={programOptions}
                  optionLabel="label"
                  optionValue="value"
                  placeholder="Select a program"
                  required
                  error={
                    validationErrors.program ? 'This field is required' : ''
                  }
                />
                <SelectInput
                  label="Location"
                  value={location}
                  height={'45px'}
                  onChange={(e) => {
                    setLocation(e.value)
                    setValidationErrors((prev) => ({
                      ...prev,
                      location: false,
                    }))
                  }}
                  style={{ flex: 1 }}
                  theme="metronic"
                  options={locationOptions}
                  optionLabel="label"
                  optionValue="value"
                  placeholder="Select a location"
                  required
                  error={
                    validationErrors.location ? 'This field is required' : ''
                  }
                />
                <SelectInput
                  label="Class Time"
                  value={classTime}
                  height={'45px'}
                  onChange={(e) => {
                    setClassTime(e.value)
                    setValidationErrors((prev) => ({
                      ...prev,
                      classTime: false,
                    }))
                  }}
                  style={{ flex: 1 }}
                  theme="metronic"
                  options={classTimeOptions}
                  optionLabel="label"
                  optionValue="value"
                  placeholder="Select a class time"
                  required
                  error={
                    validationErrors.classTime ? 'This field is required' : ''
                  }
                />
                <SelectInput
                  label="Instruction Method"
                  value={instructionMethod}
                  height={'45px'}
                  onChange={(e) => {
                    setInstructionMethod(e.value)
                    setValidationErrors((prev) => ({
                      ...prev,
                      instructionMethod: false,
                    }))
                  }}
                  style={{ flex: 1 }}
                  theme="metronic"
                  options={instructionMethodOptions}
                  optionLabel="label"
                  optionValue="value"
                  placeholder="Select an instruction method"
                  required
                  error={
                    validationErrors.instructionMethod
                      ? 'This field is required'
                      : ''
                  }
                />
                <SelectInput
                  label="Cohort"
                  value={cohort}
                  height={'45px'}
                  onChange={(e) => {
                    setCohort(e.value)
                    setValidationErrors((prev) => ({ ...prev, cohort: false }))
                  }}
                  style={{ flex: 1 }}
                  theme="metronic"
                  options={cohortOptions}
                  optionLabel="label"
                  optionValue="value"
                  placeholder="Select a cohort"
                  required
                  error={
                    validationErrors.cohort ? 'This field is required' : ''
                  }
                />
                <SelectInput
                  label="Duration"
                  value={duration}
                  height={'45px'}
                  onChange={(e) => {
                    setDuration(e.value)
                    setValidationErrors((prev) => ({
                      ...prev,
                      duration: false,
                    }))
                  }}
                  style={{ flex: 1 }}
                  theme="metronic"
                  options={months}
                  optionLabel="label"
                  optionValue="value"
                  placeholder="Select a duration"
                  required
                  error={
                    validationErrors.duration ? 'This field is required' : ''
                  }
                />
                <TextInput
                  label="Start Date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  style={{ flex: 1 }}
                  theme="metronic"
                  placeholder="MM/DD/YYYY"
                  readOnly={true}
                />
                <TextInput
                  label="End Date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  style={{ flex: 1 }}
                  theme="metronic"
                  placeholder="MM/DD/YYYY"
                  readOnly={true}
                />
              </div>
              <div
                className="mt-5"
                style={{
                  display: 'flex',
                  justifyContent: 'end',
                  gap: 20,
                }}
              >
                <Button
                  label="Back"
                  theme="metronic"
                  variant="outline"
                  style={{ minWidth: 100 }}
                  onClick={handleBack}
                />
                <Button
                  label="Next"
                  theme="metronic"
                  style={{ minWidth: 100 }}
                  onClick={handleNext}
                />
              </div>
            </>
          )}
          {step === 3 && (
            <>
              <div className={clsx('font-bold text-lg mb-4', styles.fontText)}>
                Financial Aid
              </div>
              <div className="flex flex-column gap-4 ">
                <SelectInput
                  label="Available"
                  height={'45px'}
                  value={aidAvailable}
                  onChange={(e) => {
                    setAidAvailable(e.value)
                    setValidationErrors((prev) => ({
                      ...prev,
                      aidAvailable: false,
                    }))
                    // Reset aid type when availability changes
                    if (!e.value) {
                      setAidType('')
                      setAidFiles([])
                    }
                  }}
                  style={{ flex: 1 }}
                  theme="metronic"
                  options={[
                    { label: 'Yes', value: true },
                    { label: 'No', value: false },
                  ]}
                  optionLabel="label"
                  optionValue="value"
                  required
                  error={
                    validationErrors.aidAvailable
                      ? 'This field is required'
                      : ''
                  }
                />
                {aidAvailable && (
                  <>
                    <SelectInput
                      label="Type"
                      height={'45px'}
                      value={aidType}
                      onChange={(e) => {
                        setAidType(e.value)
                        setValidationErrors((prev) => ({
                          ...prev,
                          aidType: false,
                        }))
                      }}
                      style={{ flex: 1 }}
                      theme="metronic"
                      options={aidTypeOptions}
                      optionLabel="label"
                      optionValue="value"
                      required={aidAvailable}
                      error={
                        validationErrors.aidType ? 'This field is required' : ''
                      }
                    />
                    <div style={{ marginBottom: 16 }}>
                      <div
                        className={clsx(
                          'font-semibold text-base mt-4',
                          styles.fontText
                        )}
                      >
                        Add Attachments
                      </div>
                      <div
                        className="mt-2"
                        style={{
                          margin: 0,
                          paddingLeft: 0,
                          display: 'flex',
                          flexWrap: 'wrap',
                        }}
                      >
                        {aidFiles.map((file, i) => (
                          <div
                            key={i}
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              marginBottom: 10,
                              border: '1px solid #1B84FF',
                              borderRadius: 10,
                              padding: '6px',
                              fontSize: 15,
                              width: 'calc(50% - 8px)',
                              marginRight: i % 2 === 0 ? '16px' : '0',
                              color: '#1B84FF',
                            }}
                          >
                            <span style={{ flex: 1 }}>{file.name}</span>
                            <Image
                              src={deleteIcon}
                              alt="delete"
                              onClick={() => handleDeleteFile(i)}
                              className="cursor-pointer"
                            />
                          </div>
                        ))}
                      </div>
                      <input
                        type="file"
                        multiple
                        ref={fileInputRef}
                        style={{ display: 'none' }}
                        onChange={handleFileUpload}
                      />
                      <a
                        style={{
                          cursor: 'pointer',
                          textDecoration: 'underline',
                          color: 'blue',
                        }}
                        onClick={() =>
                          fileInputRef.current && fileInputRef.current.click()
                        }
                      >
                        Add Files
                      </a>
                    </div>
                  </>
                )}
              </div>
              <div
                className="mt-5"
                style={{
                  display: 'flex',
                  justifyContent: 'end',
                  gap: 20,
                }}
              >
                <Button
                  label="Back"
                  variant="outline"
                  theme="metronic"
                  style={{ minWidth: 100 }}
                  onClick={handleBack}
                />
                <Button
                  label="Save"
                  theme="metronic"
                  loading={isLoading}
                  style={{ minWidth: 100 }}
                  onClick={() => {
                    if (validateStep3()) {
                      onSubmit &&
                        onSubmit({
                          firstName,
                          lastName,
                          email,
                          dob,
                          program,
                          location,
                          classTime,
                          instructionMethod,
                          cohort,
                          duration,
                          startDate,
                          endDate,
                          aidAvailable,
                          aidType,
                          aidFiles,
                          documents,
                        })
                      setStep(1)
                      setFirstName('')
                      setLastName('')
                      setEmail('')
                      setDob('')
                      setProgram([])
                      setLocation('')
                      setClassTime('')
                      setInstructionMethod('')
                      setCohort('')
                      setDuration('')
                      setStartDate('')
                      setEndDate('')
                      setAidAvailable(null)
                      setAidType('')
                      setAidFiles([])
                      setDocuments([])
                      onHide()
                    }
                  }}
                />
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

const PersonalInformation = ({ leadDetails, setLeadDetails, handleUpdate }) => {
  const [primaryPhone, setPrimaryPhone] = useState('+16478571072')
  const [showMailModal, setShowMailModal] = useState(false)
  const [formData, setFormData] = useState({
    firstName: leadDetails?.firstName || '',
    lastName: leadDetails?.lastName || '',
    primaryEmail: leadDetails?.primaryEmail || '',
    primaryPhone: leadDetails?.primaryPhone || '',
    secondaryEmail: leadDetails?.secondaryEmail || '',
    secondaryPhone: leadDetails?.secondaryPhone || '',
    leadSource: leadDetails?.leadSource || '',
  })
  const toast = useRef(null)

  useEffect(() => {
    setFormData({
      firstName: leadDetails?.firstName || '',
      lastName: leadDetails?.lastName || '',
      primaryEmail: leadDetails?.primaryEmail || '',
      primaryPhone: leadDetails?.primaryPhone || '',
      secondaryEmail: leadDetails?.secondaryEmail || '',
      secondaryPhone: leadDetails?.secondaryPhone || '',
      leadSource: leadDetails?.leadSource || '',
    })
  }, [leadDetails])

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleSubmit = () => {
    const updatedLeadDetails = {
      leadId: leadDetails.id,
      agentId: leadDetails.agentId,
      firstName: formData.firstName,
      lastName: formData.lastName,
      primaryEmail: formData.primaryEmail,
      secondaryEmail: formData.secondaryEmail,
      primaryPhone: formData.primaryPhone,
      secondaryPhone: formData.secondaryPhone,
      leadSource: formData.leadSource,
      preferredContactMethod: leadDetails.preferredContactMethod,
      bestTimeToCall: leadDetails.bestTimeToCall,
      isStudent: leadDetails.isStudent,
      preferredRegion: leadDetails.preferredRegion,
      enrollingIn: leadDetails.enrollingIn,
      instructionMethod: leadDetails.instructionMethod,
      address: leadDetails.address,
      communicationPreferences: leadDetails.communicationPreferences,
      leadPrograms: leadDetails.leadPrograms,
    }
    handleUpdate(updatedLeadDetails)
  }

  return (
    <div className={clsx('p-0', styles.gridCard)}>
      <div className={styles.formTitle}>Personal Information</div>
      <div className={` ${styles.inputGroup} ${styles.formSide}`}>
        <Toast ref={toast} className="custom-lead" />
        <div className="flex w-full gap-4 mt-4">
          <TextInput
            label="First Name"
            name="firstName"
            placeholder="Enter your first name"
            value={formData.firstName}
            onChange={handleInputChange}
            theme="metronic"
          />
          <TextInput
            label="Last Name"
            name="lastName"
            placeholder="Enter your last name"
            value={formData.lastName}
            onChange={handleInputChange}
            theme="metronic"
          />
        </div>
        <div className="flex w-full gap-4 mt-4">
          <div className="relative w-full flex justify-content-end align-items-center">
            <TextInput
              label="Primary Email"
              name="primaryEmail"
              placeholder="Enter your primary email"
              value={formData.primaryEmail}
              onChange={handleInputChange}
              theme="metronic"
            />
            <div
              className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
              style={{ paddingTop: '2.25rem', paddingRight: '0.25rem' }}
            >
              <Image
                src={mail}
                alt="mail"
                onClick={() => setShowMailModal(true)}
              />
            </div>
            {showMailModal && (
              <MailComponent
                leadName={`${formData.firstName} ${formData.lastName}`}
                leadId={leadDetails?.id}
                toastRef={toast}
                emailTo={formData.primaryEmail}
                visible={showMailModal}
                onHide={() => setShowMailModal(false)}
              />
            )}
          </div>
          <div className="relative w-full flex justify-content-end align-items-center">
            <TextInput
              label="Primary Phone"
              name="primaryPhone"
              placeholder="Enter your primary phone"
              value={formData.primaryPhone}
              onChange={handleInputChange}
              theme="metronic"
              className="pr-10"
            />
            <SendSmsButton
              personalInfo={{ ...formData, leadId: leadDetails?.id }}
              phoneNumber={formData.primaryPhone}
            />
            <PhoneCallComponent
              personalInfo={{ ...formData, leadId: leadDetails?.id }}
              phoneNumber={formData.primaryPhone}
              type="primary"
            />
          </div>
        </div>
        <div className="flex w-full gap-4 mt-4">
          <div className="relative w-full flex justify-content-end align-items-center">
            <TextInput
              label="Secondary Email"
              name="secondaryEmail"
              placeholder="Enter your secondary email"
              value={formData.secondaryEmail}
              onChange={handleInputChange}
              theme="metronic"
            />
            <div
              className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
              style={{ paddingTop: '2.25rem', paddingRight: '0.25rem' }}
            >
              <Image
                src={mailDisable}
                alt="mail"
                onClick={() => setShowMailModal(true)}
              />
            </div>
          </div>
          <div className="relative w-full flex justify-content-end align-items-center">
            <TextInput
              label="Secondary Phone"
              name="secondaryPhone"
              placeholder="Enter your secondary phone"
              value={formData.secondaryPhone}
              onChange={handleInputChange}
              theme="metronic"
              className="pr-10"
            />
            <SendSmsButton
              disabled={!formData.secondaryPhone}
              personalInfo={{ ...formData, leadId: leadDetails?.id }}
              phoneNumber={formData.secondaryPhone}
            />
            <PhoneCallComponent
              disabled={!formData.secondaryPhone}
              personalInfo={{ ...formData, leadId: leadDetails?.id }}
              phoneNumber={formData.secondaryPhone}
              type="primary"
            />
          </div>
        </div>
        <div className="flex w-full gap-4 mt-4">
          <SelectInput
            label="Active User Type"
            name="activeUserType"
            value={leadDetails?.isStudent ? 'Student' : 'Prospect'}
            onChange={handleInputChange}
            options={['Prospect', 'Student']}
            loading={false}
            filter
            readOnly={true}
            filterBy="name"
            theme="metronic"
            placeholder="Select active user type"
          />
          <SelectInput
            label="Prospect Source"
            name="leadSource"
            value={formData.leadSource}
            onChange={handleInputChange}
            options={['Messages', 'Call', 'Email', 'Any']}
            loading={false}
            filter
            filterBy="name"
            theme="metronic"
            placeholder="select prospect source"
          />
        </div>
        <div className="flex w-full mt-5">
          <Button
            label="Update"
            theme="metronic"
            style={{ minWidth: 100, marginLeft: 'auto' }}
            onClick={handleSubmit}
          />
        </div>
      </div>
    </div>
  )
}
