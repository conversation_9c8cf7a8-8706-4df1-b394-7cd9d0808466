.tabsCont {
  display: flex;
  flex-direction: column;
  row-gap: 1rem;
  height: 92dvh;
  margin-top: 1rem;
}

.headerCont {
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  color: 024f7c;
  font-weight: bold;
  font-size: 22px/16px;
}

.backIcon {
  display: flex;
  justify-content: flex-start;
  margin: 0.2rem;
}

.sectionTitle {
  display: flex;
  height: 100%;
}

.sectionField {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  margin: 0.2rem;
}

.inputSection {
  display: flex;
  flex-direction: column;
  row-gap: 0.5rem;
}

.inputContainer {
  display: flex;
  column-gap: 1.5rem;
  align-items: center;
}

.inputInnerSec {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.inputField {
  display: flex;
  column-gap: 0.5rem;
}

.ColumnStyle {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.widthFull {
  width: 100%;
}

.inputMainContainer {
  margin-left: 200px;
  margin-right: 200px;
}

.inputInnerContainer {
  display: flex;
  flex-direction: column;
  row-gap: 20px;
  padding: 15px;
}

.templateBuilder {
  height: 92dvh;
  display: flex;
  gap: 1rem;
}

.templateBuilderBox {
  height: 100%;
  width: 65%;
  background-color: #fff;
  border-radius: 0.5rem;
  padding: 1rem;
  overflow-y: scroll;
  row-gap: 20px;
  display: flex;
  flex-direction: column;
}

.inputWidthContainer {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.templateBuilderInput {
  display: flex;
  gap: 1rem;
  align-items: center;
  padding: 0.5rem;
}

.templateBuilderLogo {
  height: 100px;
  width: 100px;
  background-image: 'url("https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRK4VQ5dC2ZMKxY_fQ8VjybwLyIeUPUp0i7kBYEkRyVSLCYav2fI7wprFDOhbiADfFvUm0&usqp=CAU")';
  background-repeat: no-repeat;
  background-size: contain;
  aspect-ratio: auto;
}

.previewContent {
  border: none;
  scrollbar-gutter: stable;
}

.previewBox {
  height: 90%;
  width: 100%;
}

.previewContainer {
  height: 100%;
  width: 35%;
  background-color: #fff;
  border-radius: 0.5rem;
  padding: 1rem;
  overflow-y: scroll;
}

.starSymbol {
  color: red;
}

.saveIconBox {
  background-color: rgb(242, 246, 248);
  width: 50px;
  height: 3rem;
  border-radius: 5px;
  margin-left: auto;
  cursor: pointer;
  justify-content: center;
  display: flex;
  align-items: center;
}

/* .saveImgIcon:hover{
    background-image: url("../../svg/Common/Sky_blue_save.svg");
    background-repeat: no-repeat;
    transition: opacity 0.3s ease-in-out;
    position: absolute;
    top: 0;
    left: 0;
} */
.saveIconBox:hover {
  background-color: #e5e8ea;
}

.dragDropText {
  font-size: 16px;
  color: var(--primary-bg-blackPearl);
  font-weight: 600;
  font-family: Open Sans;
}

.emptyCard {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 5px;
  background: #ffffff !important;
  padding: 2rem 1rem;
  border: 2px dashed #a1a1a1 !important;
  color: #495057 !important;
  border-radius: 14px !important;
  height: 10vw;
  overflow-y: scroll;
}

.createButton {
  height: 50px;
  width: 150px;
  font-weight: 600;
  border-radius: 5px;
  background-color: var(--primary-bg-darkCerulean);
  color: #fff;
  border: none;
  font-family: 'Open Sans';
  font-size: 16px;
  cursor: pointer;
  justify-content: center;
}

.buttonWeb {
  display: block;

  @media (max-width: 768px) {
    display: none;
  }
}

.headerSection {
  display: flex;
  flex-direction: row;
  align-items: center;
}
