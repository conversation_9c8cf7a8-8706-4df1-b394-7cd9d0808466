@import url('https://fonts.googleapis.com/css2?family=Zeyada&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700&display=swap');

@font-face {
  font-family: 'WorkSans-Medium';
  src: url('../fonts/WorkSans-Medium.eot');
  src: url('../fonts/WorkSans-Medium.eot?#iefix') format('embedded-opentype'), url('../fonts/WorkSans-Medium.woff2') format('woff2'),
    url('../fonts/WorkSans-Medium.woff') format('woff'), url('../fonts/WorkSans-Medium.ttf') format('truetype'),
    url('../fonts/WorkSans-Medium.svg#WorkSans-Medium') format('svg'), url('../fonts/WorkSans-Medium.otf') format('otf');
}

@font-face {
  font-family: 'AstaginaSignature';
  src: url('../fonts/AstaginaSignature.eot');
  src: url('../fonts/AstaginaSignature.eot?#iefix') format('embedded-opentype'), url('../fonts/AstaginaSignature.woff2') format('woff2'),
    url('../fonts/AstaginaSignature.woff') format('woff'), url('../fonts/AstaginaSignature.ttf') format('truetype'),
    url('../fonts/AstaginaSignature.svg#AstaginaSignature') format('svg'), url('../fonts/AstaginaSignature.otf') format('otf');
}

@font-face {
  font-family: 'LavishlyYours-Regular';
  src: url('../fonts/LavishlyYours-Regular.eot');
  src: url('../fonts/WorkSans-Medium.eot?#iefix') format('embedded-opentype'), url('../fonts/LavishlyYours-Regular.woff2') format('woff2'),
    url('../fonts/LavishlyYours-Regular.woff') format('woff'), url('../fonts/LavishlyYours-Regular.ttf') format('truetype'),
    url('../fonts/LavishlyYours-Regular.svg#LavishlyYours-Regular') format('svg'), url('../fonts/LavishlyYours-Regular.otf') format('otf');
}

@font-face {
  font-family: 'MySoul-Regular';
  src: url('../fonts/MySoul-Regular.eot');
  src: url('../fonts/MySoul-Regular.eot?#iefix') format('embedded-opentype'), url('../fonts/MySoul-Regular.woff2') format('woff2'),
    url('../fonts/MySoul-Regular.woff') format('woff'), url('../fonts/MySoul-Regular.ttf') format('truetype'),
    url('../fonts/MySoul-Regular.svg#MySoul-Regular') format('svg'), url('../fonts/MySoul-Regular.otf') format('otf');
}

@font-face {
  font-family: 'Updock-Regular';
  src: url('../fonts/Updock-Regular.eot');
  src: url('../fonts/Updock-Regular.eot?#iefix') format('embedded-opentype'), url('../fonts/Updock-Regular.woff2') format('woff2'),
    url('../fonts/Updock-Regular.woff') format('woff'), url('../fonts/Updock-Regular.ttf') format('truetype'),
    url('../fonts/Updock-Regular.svg#Updock-Regular') format('svg'), url('../fonts/Updock-Regular.otf') format('otf');
}

@import url('https://bossanova.uk/jspreadsheet/v4/jexcel.css');
/* @import url("https://bossanova.uk/jspreadsheet/v4/jexcel.themes.css"); */
@import url('https://jsuites.net/v4/jsuites.css');

:root {
  --primary-bg-blackPearl: #002138;
  --primary-bg-blackPearl-70: #4c6373;
  --primary-bg-blackPearl-50: #7f8f9b;
  --primary-bg-blackPearl-20: #ccd2d7;
  --primary-bg-blackPearl-10: #e5e8ea;
  --primary-bg-blackPearl-5: #f2f3f5;
  --primary-bg-blackPearl-3: #f7f8f8;

  --primary-bg-darkCerulean: #024f7c;
  --primary-bg-darkCerulean-70: #4d83a3;
  --primary-bg-darkCerulean-50: #80a6bd;
  --primary-bg-darkCerulean-20: #ccdce5;
  --primary-bg-darkCerulean-10: #e5edf2;
  --primary-bg-darkCerulean-5: #f2f6f8;
  --primary-bg-darkCerulean-3: #f7f9fb;

  --secondary-bg-deepBlueSky: #00b9ff;
  --secondary-bg-deepBlueSky-70: #4cceff;
  --secondary-bg-deepBlueSky-50: #7fdcff;
  --secondary-bg-deepBlueSky-20: #ccf1ff;
  --secondary-bg-deepBlueSky-10: #e5f8ff;
  --secondary-bg-deepBlueSky-5: #f2fcff;
  --secondary-bg-deepBlueSky-3: #f7fdff;

  --secondary-bg-goldenPoppy: #fdc500;
  --secondary-bg-goldenPoppy-70: #fdd64c;
  --secondary-bg-goldenPoppy-50: #fee27f;
  --secondary-bg-goldenPoppy-20: #fff4cc;
  --secondary-bg-goldenPoppy-10: #fff9e5;
  --secondary-bg-goldenPoppy-5: #fffcf2;
  --secondary-bg-goldenPoppy-3: #fffdf7;

  --tertiary-bg-limeGreen: #07a606;
  --tertiary-bg-limeGreen-20: #cdedcd;

  --sidebar-bg: #17354b;
  --sidebar-bg-hover: #1f4f6b;

  --top-navbar-padding: 4.4rem;
  --message-container-height: 61vh;

  --primary-color: #007ad9; /* Primary color for buttons and highlights */
  --text-light: #ffffff; /* Light text color */
  --text-dark: #333333; /* Dark text color */
  --background-light: #ffffff; /* Light background color */
  --background-dark: #f4f4f4; /* Dark background color */
  --border-color: #e0e0e0; /* Border color */
  --shadow-color: rgba(0, 0, 0, 0.1); /* Shadow color */
  --highlight-color: #00b9ff; /* Highlight color for modals and accents */
  --secondary-color: #024f7c; /* Secondary color for additional highlights */
  --disabled-color: #cccccc; /* Disabled state color */
}

html,
body {
  margin: 0;
  font-family: 'Open Sans', sans-serif;
}
.full-screen {
  height: calc(var(--vh, 1vh) * 100);
}

.p-accordion.p-component {
  width: 100%;
}

.custom-versioning-modal .p-dialog-content {
  height: 30rem;
  overflow: hidden;
}

.p-datatable .custom-expander-column .p-row-toggler {
  transform: rotate(90deg);
  transition: transform 0.3s ease;
  width: 3rem;
  height: 3rem;
}

.p-datatable .custom-expander-column .p-row-toggler:hover {
  border-radius: 3px;
  width: 3rem;
  height: 3rem;
}

.p-datatable .custom-expander-column .p-row-toggler[aria-expanded='true'] {
  transform: rotate(180deg);
}

.p-calendar .p-inputtext {
  height: 50px;
  /* Used for the calendar and time fields in the conditionals modal of the Queries module */
}

.navbarTopPadding {
  padding-top: 4.4rem;
}

.pauseScroll {
  overflow-y: hidden;
}

.mlr-05 {
  margin-left: calc(100% / 24);
  margin-right: calc(100% / 24);
}

* {
  box-sizing: border-box;
}

.form-horizontal {
  margin: 0 auto;
}

/* deleted alert */

.p-datatable-table {
  border-spacing: 0;
  width: 100%;
  margin-top: 0rem !important;
}

/* Mobile Preview */
@media (max-width: 450px) {
  .p-autocomplete-panel {
    width: 95% !important;
  }
}

.p-datatable .p-datatable-thead > tr > th {
  text-align: left;
  font-weight: 700;
  font-family: 'Open Sans';
  font-size: 22px;
  padding: 1rem 1rem;
  border: 1px solid #dee2e6 !important;
  border-width: 0 0 1px 0 !important;
  color: #002138 !important;
  background: var(--primary-bg-blackPearl-20) !important;
  transition: box-shadow 0.2s !important;
}

.p-datatable .p-datatable-header {
  background-color: white !important;
  border: none !important;
  /* padding: 8px 0px; */
  padding: 0px 0px 18px 0px;
}

.p-datatable .p-datatable-tbody > tr > td {
  /* text-align: left; */
  border-width: 0 0 1px 0;
  padding: 1rem 1rem;
  font-weight: 600 !important;
  font-size: 14px;
  font-family: 'Open Sans';
  border: none;
}

.custom-purchaseReq-Datatable .p-datatable-tbody > tr > td {
  border-width: 0 0 1px 0;
  padding: 1rem 1rem;
  font-weight: 600 !important;
  font-size: 14px;
  font-family: 'Open Sans';
  border: none;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.custom-signOrder .p-inputtext {
  width: 2vw;
  height: 2.5rem;
  padding-right: 0;
  padding-left: 0;
  text-align: center;
}

.p-button.p-button-outlined {
  background-color: transparent !important;
  border: 1px solid !important;
  border-color: #80a6bd !important;
}

.p-datatable .p-datatable-tbody > tr {
  transition: box-shadow 0.2s;
  font-size: 12px !important;
  font-weight: 700;
  color: #002138 !important;
  height: 48px;
}

.p-datatable.p-datatable-striped .p-datatable-tbody > tr.p-row-odd {
  background: var(--secondary-bg-deepBlueSky-10) !important;
}

.p-datatable.p-datatable-striped .p-datatable-tbody > tr.p-row-odd.p-highlight {
  background: var(--primary-bg-darkCerulean) !important;
}

.p-datatable .p-datatable-tbody > tr.p-highlight {
  background: var(--primary-bg-darkCerulean) !important;
  color: #f7f9fa !important;
}

.p-datatable.p-datatable-selectable .p-datatable-tbody > tr.p-selectable-row:focus {
  outline: #00b9ff !important;
  background-color: var(--primary-bg-darkCerulean) !important;
  color: white !important;
}

.p-datatable.p-datatable-selectable .p-datatable-tbody > tr.p-selectable-row:focus a {
  color: white;
}

.p-datatable .p-datatable-tbody > tr.p-selectable-row:nth-child(even) {
  background-color: #ffffff;
}

.p-datatable .p-column-header-content {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.p-datatable .p-column-resizer {
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  margin: 0;
  height: 100%;
  padding: 0px;
  cursor: col-resize;
  border: 1px solid black;
  width: 0;
}

.custom-paginator.p-paginator {
  padding: 0;
}

.p-paginator {
  justify-content: right !important;
  padding: 3rem 1rem;
  border-width: 0;
}

.p-paginator .p-paginator-pages .p-paginator-page.p-highlight {
  background: rgba(2, 79, 124, 0.05);
  color: var(--primary-bg-blackPearl);
  font-size: 16px;
}

.p-paginator .p-paginator-pages .p-paginator-page {
  font-size: 16px;
}

.myTable .table-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 700;
  font-size: 20px;
}

pre::-webkit-scrollbar {
  background: transparent;
  width: 10px;
}

pre::-webkit-scrollbar-thumb {
  background: #999;
}

/* .bgPrimary {
  background-color: #003459;
} */

/* .colorPrimary {
  color: #003459;
} */

/* p-inputtext check  */
input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: auto;
  appearance: auto;
  height: 30px;
  padding: 5px;
  background-color: #f0f0f0;
}

.p-inputtext {
  margin: 0;
  /* color: #09547f !important; */
  color: var(--primary-bg-darkCerulean);
  font-size: 14px !important;
  font-weight: 600;
  font-family: 'Open Sans';
  /* 
    height: 50px; <- Just like the height property that is commented below, this is causing UI issues for components in the form builder.
    Please, if adding this height property to this specific CSS class is mandatory for your task(s), contact Alex on Teams to discuss the issue.
  */
  border-radius: 5px;
  /* 
  height: 52px; <- This was causing some UI issues for the components in the form builder. 
  Please use a more specific class name if you need to set the height for specific input text fields.
  */
  width: 100%;
  height: 50px;
  padding-left: 16px;
  padding-right: 2.5rem;
}

.p-inputtext::placeholder {
  opacity: 0.5;
}

.p-tempStyle {
  margin: 0;
  height: 52px;
}

.p-tooltip .p-tooltip-text {
  font-size: 12px !important;
  box-shadow: none !important;
}

/* 
  ^This is temporary and will be removed once all prime react
  components have been replaced by custom components (see Inputs folder) 
*/

.p-tempStyle .p-inputtext {
  padding: 16px 14px;
  font-size: 16px;
  border: 1px solid #024f7c20;
  border-radius: 5px;
  color: var(--primary-bg-darkCerulean);
}

.p-tempStyle .p-inputtext::placeholder {
  color: var(--primary-bg-darkCerulean);
}

/* text-whit check */

.text-whit:hover {
  color: white !important;
}

.p-fileupload .p-fileupload-buttonbar .p-button {
  margin-right: 0.5rem;
}

.pi:before {
  --webkit-backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.p-tree .p-tree-container .p-treenode .p-treenode-content .pi-home {
  color: #ffffff;
}

.p-tree .p-tree-container .p-treenode .p-treenode-content .pi-sitemap {
  color: #ffffff;
}

.p-tree .p-tree-container .p-treenode .p-treenode-content .pi-inbox {
  color: #ffffff;
}

.p-tree .p-tree-container .p-treenode .p-treenode-content .pi-file {
  color: #ffffff;
}

.p-tree .p-tree-container .p-treenode .p-treenode-content .pi-file {
  color: #ffffff;
}

.p-panelmenu .p-panelmenu-header > a {
  padding: 1rem;
  border: 1px solid #024f7c !important;
  color: white !important;
  background: #024f7c !important;
  font-size: 0.9em !important;
}

.p-fileupload .p-fileupload-content {
  background: #ffffff !important;
  padding: 2rem 1rem;
  border: 2px dashed #a1a1a1 !important;
  color: #495057 !important;
  /* border-bottom-right-radius: 3px; */
  /* border-bottom-left-radius: 3px; */
  border-radius: 14px !important;
  /* min-height: 180px;
  height: max-content; */
  height: 10vw;
  overflow-y: scroll;
  scrollbar-width: none;
}

.p-fileupload .p-fileupload-buttonbar {
  border: none !important;
}

.p-card {
  margin: 20px;
  border-radius: 10px;
  background-color: #fff;
  box-shadow: none;
}

.p-card .p-card-title {
  font-size: 1.17em !important;
  font-family: Open Sans;
  color: var(--primary-bg-blackPearl);
}

.customFullWidth .p-dropdown .p-dropdown-label {
  width: 0rem;
}

.p-dropdown-item-label {
  width: 100%;
}

.p-dropdown .p-dropdown-label {
  background: transparent;
  border: 0 none;
  color: #09547f !important;
  display: flex;
  align-items: center;
  text-transform: capitalize;
}

.p-connectDropdown span.p-dropdown-label.p-inputtext {
  padding: 16px 14px;
}

.p-button.p-fileupload-choose {
  position: relative;
  overflow: hidden;
  background-color: #024f7c;
  width: 122px;
  margin-left: -17px;
}

.p-tree-container {
  /* height: 100vh; */
  overflow: auto !important;
}

.p-tree .p-tree-container .p-treenode .p-treenode-content .p-tree-toggler {
  margin-right: 0.5rem;
  width: 2rem;
  height: 2rem;
  color: #ffffff !important;
  border: 0 none;
  background: transparent;
  border-radius: 50%;
  transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
}

.p-treenode-label {
  color: white;
  font-size: 12px;
  font-weight: 500;
}

.p-tree .p-tree-container .p-treenode .p-treenode-content.p-treenode-selectable:not(.p-highlight):hover {
  background: none;
}

.p-tree .p-treenode-children {
  padding-left: 0;
}

ul.tree {
  list-style: none;
  margin-top: 0;
}

ul.tree li {
  position: relative;
  padding: 2rem;
  padding-bottom: 0rem;
}

ul.tree li::before {
  position: absolute;
  left: -1rem;
  top: 0px;
  border-left: 2.5px solid #ccdce5;
  border-bottom: 2.5px solid #ccdce5;
  content: '';
  width: 4rem;
  height: 3.5rem;
}

ul.tree li::after {
  position: absolute;
  left: -1rem;
  bottom: 0px;
  border-left: 2.5px solid #ccdce5;
  content: '';
  width: 4rem;
  height: 100%;
}

ul.tree li:last-child::after {
  display: none;
}

@media (max-width: 450px) {
  ul.tree li::before {
    border-bottom: none !important;
  }
}

div.menutree > :nth-child(2) {
  position: absolute;
}

.p-autocomplete .p-autocomplete-multiple-container {
  padding: 10px 16px;
  font-size: 16px;
}

.p-autocomplete-multiple-container {
  width: 100%;
}

/* ul.tree,
ul.tree ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

ul.tree ul {
  margin-left: 10px;
}

ul.tree li {
  margin: 0;
  padding: 4px 7px;
  line-height: 20px;
  color: #003549;
  font-weight: bold;
  border-left: 2px solid #CCDCE5;
  font-size: small;
  font-family: 'Open Sans';
  margin-left: 2rem;

}

ul.tree li:last-child {
  border-left: none;
}

ul.tree li:before {
  position: relative;
  top: -0.3em;
  height: 2em;
  width: 12px;
  color: white;
  border-bottom: 2px solid #CCDCE5;
  content: "";
  display: inline-block;
  left: -7px;
}

ul.tree li:last-child:before {
  border-left: 2px solid #CCDCE5;
}

.tree {
  --spacing: 1.5rem;
  --radius: 10px;
}

.tree li {
  display: block;
  position: relative;
  padding-left: calc(2 * var(--spacing) - var(--radius) - 2px);
} */

/* ul.tree ul {
  margin-left: 10px;
}

ul.tree li {
  margin: 0;
  padding: 4px 7px;
  line-height: 20px;
  color: #003549;
  font-weight: bold;
  border-left: 1px solid rgb(100, 100, 100);
  font-size: small;
  font-family: 'Open Sans';

}

ul.tree li:last-child {
  border-left: none;
} */

/* ul.tree li:before {
  position: relative;
  top: -0.3em;
  height: 2em;
  width: 12px;
  color: white;
  border-bottom: 1px solid rgb(100, 100, 100);
  content: "";
  display: inline-block;
  left: -7px;
}

ul.tree li:last-child:before {
  border-left: 1px solid rgb(100, 100, 100);
}

.tree {
  --spacing: 1.5rem;
  --radius: 10px;
}

.tree li {
  display: block;
  position: relative;
  padding-left: calc(2 * var(--spacing) - var(--radius) - 2px);
}

.tree ul {
  margin-left: calc(var(--radius) - var(--spacing));
  padding-left: 0;
} */

/* .tree ul::before{
  position: absolute;
  content: "+";
  background-color:palegreen;
  display:block;
  width: 12px;
  height: 12px;
  border-radius: 50em;
  left:14px;
  top:1em
}  
.glyphicon {
  position: relative;
  top: 1px;
  display: inline-block;
  font-family: 'Glyphicons Halflings';
  font-style: normal;
  font-weight: 400;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
} */

.p-card .p-card-body {
  padding: 1.3rem !important;
}

.p-card .p-card-content {
  padding: 0 !important;
}

.p-fileupload-content > .p-progressbar {
  background: none !important;
}

/* index.js and upload (down below) */

.lds-dual-ring {
  display: inline-block;
  width: 30px;
  height: 30px;
}

.lds-dual-ring:after {
  content: ' ';
  display: block;
  width: 12px;
  height: 12px;
  margin: 8px;
  border-radius: 50%;
  border: 3px solid #024f7c50;
  border-color: #024f7c50 transparent #024f7c50 transparent;
  animation: lds-dual-ring 1.2s linear infinite;
  margin-top: 4px;
  align-items: center;
  justify-content: center;
  margin-top: 7px;
}

@keyframes lds-dual-ring {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.lds-spinner {
  /* color: official;
  display: inline-flex;
  position: absolute;
  z-index: 9999;
  margin-left: 15rem;
  width: 80px;
  height: 80px;
  bottom: 4rem; */
  justify-content: center;
  display: flex;
  /* position: absolute;
  z-index: 9999; */
}

.lds-spinner div {
  transform-origin: 40px 40px;
  animation: lds-spinner 1.2s linear infinite;
}

.lds-spinner div:after {
  content: ' ';
  display: block;
  position: absolute;
  top: 3px;
  left: 37px;
  width: 6px;
  height: 18px;
  border-radius: 20%;
  background: lightslategray;
}

.lds-spinner div:nth-child(1) {
  transform: rotate(0deg);
  animation-delay: -1.1s;
}

.lds-spinner div:nth-child(2) {
  transform: rotate(30deg);
  animation-delay: -1s;
}

.lds-spinner div:nth-child(3) {
  transform: rotate(60deg);
  animation-delay: -0.9s;
}

.lds-spinner div:nth-child(4) {
  transform: rotate(90deg);
  animation-delay: -0.8s;
}

.lds-spinner div:nth-child(5) {
  transform: rotate(120deg);
  animation-delay: -0.7s;
}

.lds-spinner div:nth-child(6) {
  transform: rotate(150deg);
  animation-delay: -0.6s;
}

.lds-spinner div:nth-child(7) {
  transform: rotate(180deg);
  animation-delay: -0.5s;
}

.lds-spinner div:nth-child(8) {
  transform: rotate(210deg);
  animation-delay: -0.4s;
}

.lds-spinner div:nth-child(9) {
  transform: rotate(240deg);
  animation-delay: -0.3s;
}

.lds-spinner div:nth-child(10) {
  transform: rotate(270deg);
  animation-delay: -0.2s;
}

.lds-spinner div:nth-child(11) {
  transform: rotate(300deg);
  animation-delay: -0.1s;
}

.lds-spinner div:nth-child(12) {
  transform: rotate(330deg);
  animation-delay: 0s;
}

@keyframes lds-spinner {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

.tagInput {
  color: limegreen !important;
  height: 40px;
}

.tagInputError {
  border: 1px solid red;
  color: red !important;
  height: 40px;
}

/* checked below  */

.dashboardTitle {
  padding: 1.625rem 1rem 1.625rem 0;
  font-weight: 700 !important;
}

.dashoardHeader {
  font-size: 15px;
  color: var(--primary-bg-darkCerulean);
}

.dashboardHeaderStyle {
  font-weight: '600';
  font-size: '15.5px';
  color: '#000';
}

.dashboardTitle:focus {
  color: white !important;
}

.p-button {
  /* background-color: #003459; //  Commented out due to the Permissions Select buttons showing up all blue on the dev environment. -Yibran
  color: #F7F9FA; */
  /* border-radius: 100px !important; */
  border: 0;
  /* height: 2rem !important; */
}

.customSelectButton.p-button.p-component.p-highlight {
  background-color: #003459 !important;
}

/* .p-button:hover {
  background-color: unset;
  color: unset;
} */

/* .p-button:enabled:hover,
.p-button:not(button):not(a):not(.p-disabled):hover {
  background-color: var(--secondary-bg-goldenPoppy);
  color: #fff;
  border-color: var(--primary-bg-blackPearl) !important;
} */

.p-dialog {
  border-radius: 10px;
}

.p-dialog .p-dialog-header {
  font-weight: bold;
  font-size: 25px/34px;
  background: #ffffff;
  color: var(--primary-bg-blackPearl);
  padding: 20px 22px;
  padding-top: 15px;
  border-top: 15px solid var(--secondary-bg-deepBlueSky);
  border-top-right-radius: 10px;
  border-top-left-radius: 10px;
  padding-bottom: 20px;
  border-bottom: 1px solid #024f7c10;
  font-family: 'Open Sans';

  @media (min-width: 373px) and (max-width: 392px) {
    margin-top: 93px;
  }
}

.p-dialog-content::-webkit-scrollbar {
  width: 8px;
}

.p-dialog-content::-webkit-scrollbar-thumb {
  background-color: var(--primary-bg-blackPearl-20);
}

.p-dialog .p-dialog-header .p-dialog-header-icon {
  width: 30px;
  height: 30px;
  color: #fff;
  background: var(--secondary-bg-deepBlueSky);
  border: 0 none;
  border-radius: 50%;
  /* margin-right: 0.5rem; */
  border-color: #00b9ff;
}

.p-dialog .p-dialog-header .p-dialog-header-icon:enabled:hover {
  color: unset;
  border-color: unset;
  background: unset;
}

.p-dialog .p-dialog-content {
  padding: 1.5rem 1.5rem;
  border-bottom-left-radius: 10px !important;
  border-bottom-right-radius: 10px !important;
}

.p-dropdown-sm .p-dropdown-trigger {
  background: transparent;
  color: black;
  width: 0.5rem !important;
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
}

.p-dropdown-sm .p-dropdown-label {
  background: transparent;
  border: 0 none;
  font-size: 1em !important;
  color: #09547f !important;
  /* margin-bottom: -19rem; */
  margin-top: -0.5rem;
}

.p-checkbox-sm .p-checkbox-box {
  border: 1.5px solid var(--primary-bg-blackPearl);
  border-radius: none !important;
  background: #ffffff;
  width: 15px !important;
  height: 15px !important;
  color: #002138 !important;
  margin-top: 0.25rem !important;
  border-radius: 3px;
  transition: background-color 0.2s, color 0.2s, border-color 0.2s box-shadow 0.2s;
}

.p-checkbox-sm .p-checkbox-box::before {
  background-color: #002138 !important;
  border-color: #002138 !important;
}

.p-checkbox-sm.p-checkbox-checked .p-checkbox-box,
.p-checkbox-sm .p-checkbox-box.p-highlight {
  background-color: #002138 !important;
  border-color: #002138 !important;
}

.p-checkbox-sm:hover .p-checkbox-box,
.p-checkbox-sm .p-checkbox-box:hover {
  background-color: #002138 !important;
  border-color: #002138 !important;
}

.p-inputswitch-slider:before {
  width: 0.8rem;
  height: 0.8rem;
  margin-top: -7px;
  left: 1px;
}

.p-inputswitch.p-highlight .p-inputswitch-slider {
  background: #024f7c;
  width: 38px;
  height: 20px;
}

.p-inputswitch .p-inputswitch-slider {
  width: 38px;
  height: 20px;
}

.p-inputswitch-sm .p-inputswitch-slider {
  background: white !important;
  transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
  border-radius: 30px;
  width: 33px !important;
  height: 16px !important;
  border: 1px solid #002138 !important;
}

.p-inputswitch-sm .p-inputswitch-slider:before {
  background: red;
  width: 0.8rem;
  height: 0.8rem;
  left: 0.1rem;
  margin-top: -0.4rem;
  border-radius: 50%;
  transition-duration: 0.2s;
}

.p-inputswitch-sm {
  margin-top: 0.3rem;
  margin-right: -0.2rem;
}

.p-inputswitch-sm.p-inputswitch-checked .p-inputswitch-slider:before {
  transform: translateX(1rem);
  background: red !important;
}

tr.p-row-odd {
  background-color: var(--secondary-bg-deepBlueSky-10) !important;
}

.multiselect-border-custom-color {
  border-bottom: 1px solid #ccdae9;
  height: 50px;
  /* Your custom color */
}

.p-multiselect {
  padding: 8px 8px;
  font-size: 16px;
  border: 1px solid #024f7c20;
  border-radius: 5px;
  color: var(--primary-bg-darkCerulean);
}

.p-multiselect:focus {
  outline: none;
  border: 1px solid #024f7c80;
}

.form-submission-multi-select .p-multiselect .p-multiselect-label {
  overflow-y: scroll;
  max-height: 50px;
  display: flex;
  flex-wrap: wrap;
  column-gap: 10px;
}

.p-inline-message.p-inline-message-error {
  border: none;
  border-radius: 5px;
  background: #fee9e9;
  color: #ff0000;
  font-weight: 500;
  justify-content: left;
}

.p-inline-message.p-inline-message-error .p-inline-message-icon {
  display: none;
}

.p-inline-message-text {
  padding-left: 0.5rem;
}

.p-inputtext.p-invalid.p-component {
  border-color: #f44336 !important;
}

/* This part doesn't show icon */
/* .p-button .p-button-icon-left {
  margin-right: 0.2rem;
  display: none !important;
} */

/* bg-color */
.bg {
  color: #fff;

  &.blackPearl {
    background-color: var(--primary-bg-blackPearl);
  }

  &.blackPearl-70 {
    background-color: var(--primary-bg-blackPearl-70);
  }

  &.blackPearl-50 {
    background-color: var(--primary-bg-blackPearl-50);
  }

  &.blackPearl-20 {
    background-color: var(--primary-bg-blackPearl-20);
  }

  &.blackPearl-10 {
    background-color: var(--primary-bg-blackPearl-10);
  }

  &.blackPearl-5 {
    background-color: var(--primary-bg-blackPearl-5);
  }

  &.blackPearl-3 {
    background-color: var(--primary-bg-blackPearl-3);
  }

  &.darkCerulean {
    background-color: var(--primary-bg-darkCerulean);
  }

  &.darkCerulean-70 {
    background-color: var(--primary-bg-darkCerulean-70);
  }

  &.darkCerulean-50 {
    background-color: var(--primary-bg-darkCerulean-50);
  }

  &.darkCerulean-20 {
    background-color: var(--primary-bg-darkCerulean-20);
  }

  &.darkCerulean-10 {
    background-color: var(--primary-bg-darkCerulean-10);
  }

  &.darkCerulean-5 {
    background-color: var(--primary-bg-darkCerulean-5);
  }

  &.darkCerulean-3 {
    background-color: var(--primary-bg-darkCerulean-3);
  }

  &.deepBlueSky {
    background-color: var(--secondary-bg-deepBlueSky);
  }

  &.deepBlueSky-70 {
    background-color: var(--secondary-bg-deepBlueSky-70);
  }

  &.deepBlueSky-50 {
    background-color: var(--secondary-bg-deepBlueSky-50);
  }

  &.deepBlueSky-20 {
    background-color: var(--secondary-bg-deepBlueSky-20);
  }

  &.deepBlueSky-10 {
    background-color: var(--secondary-bg-deepBlueSky-10);
  }

  &.deepBlueSky-5 {
    background-color: var(--secondary-bg-deepBlueSky-5);
  }

  &.deepBlueSky-3 {
    background-color: var(--secondary-bg-deepBlueSky-3);
  }

  &.goldenPoppy {
    background-color: var(--secondary-bg-goldenPoppy);
  }

  &.goldenPoppy-70 {
    background-color: var(--secondary-bg-goldenPoppy-70);
  }

  &.goldenPoppy-50 {
    background-color: var(--secondary-bg-goldenPoppy-50);
  }

  &.goldenPoppy-20 {
    background-color: var(--secondary-bg-goldenPoppy-20);
  }

  &.goldenPoppy-10 {
    background-color: var(--secondary-bg-goldenPoppy-10);
  }

  &.goldenPoppy-5 {
    background-color: var(--secondary-bg-goldenPoppy-5);
  }

  &.goldenPoppy-3 {
    background-color: var(--secondary-bg-goldenPoppy-3);
  }
}

/* label-Required */

.required::after {
  content: ' *';
  color: red;
}

/* label-Required */

/* bg-color */

/* color Primary */
.text {
  &.primary {
    color: var(--primary-bg-blackPearl);
  }
}

/* color Primary */
.p-datatable.p-datatable-sm .p-datatable-thead > tr > th {
  padding: 0.5rem 0.5rem;
  display: none;
}

.p-datatable.p-datatable-sm.p-datatable-audit-history .p-datatable-thead > tr > th {
  display: revert;
}

.p-datatable > .p-datatable-wrapper {
  border-radius: 0px !important;
  max-height: 100%;
}

.p-multiselect-panel .p-multiselect-items .p-multiselect-item.p-highlight.p-focus {
  background: #e9ecef;
}

.p-multiselect-panel .p-multiselect-items .p-multiselect-item.p-highlight {
  background: #e9ecef;
}
.p-multiselect-panel .p-multiselect-items .p-multiselect-item {
  color: #004990;
}
datalist {
  position: absolute;
  max-height: 20em;
  border: 0 none;
  overflow-x: hidden;
  overflow-y: auto;
}

datalist option {
  font-size: 0.8em;
  padding: 0.3em 1em;
  background-color: #ccc;
  cursor: pointer;
}

datalist option:hover,
datalist option:focus {
  color: #fff;
  background-color: #036;
  outline: 0 none;
}

/* color Primary */

/* width */
::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

/* Track */
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px transparent;
  border-radius: 10px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: var(--primary-bg-blackPearl-20);
  border-radius: 10px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: var(--primary-bg-blackPearl-10);
}

.p-dialog .p-dialog-header .p-dialog-title {
  font-weight: bold;
  font-size: 20px;
}

/* BreadCrumbs */
.p-breadcrumb .p-breadcrumb-list li:last-child .p-menuitem-text {
  font-family: 'Open Sans';
  font-size: 16px;
  font-weight: 600;
  padding: 0px 0px 0px 0px;
  color: rgb(0, 52, 89);

  @media only screen and (max-width: 768px) {
    font-size: 14px;
  }
}

.p-breadcrumb .p-breadcrumb-list li .p-menuitem-link .p-menuitem-icon {
  color: rgb(0, 52, 89);
}

.p-breadcrumb .p-breadcrumb-list li.p-menuitem-separator {
  color: rgb(0, 52, 89);
}

.p-breadcrumb .p-menuitem-text {
  color: #002138 !important;
  font-size: 16px;
  font-weight: 600;
  padding: 0px 0px 0px 0px;
  font-family: 'Open Sans' !important;
  text-transform: capitalize;

  @media only screen and (max-width: 768px) {
    font-size: 14px;
  }
}

.p-dropdown-panel .p-dropdown-items .p-dropdown-item {
  margin: 0;
  padding: 0.5rem 1rem;
  border: 0 none;
  color: #09547f;
  background: transparent;
  transition: box-shadow 0.2s;
  border-radius: 0;
  text-transform: capitalize;
  font-family: 'Open Sans';
  font-weight: 500;
  font-size: 14px;
  height: 50px;
}

.p-dropdown-panel .p-dropdown-header {
  padding: 20px 20px 0px 20px;
  background: white;
}

/* Number Inputs */
input.p-inputtext.p-component.p-disabled.p-filled.p-inputnumber-input.p-inputnumber-input {
  width: -webkit-fill-available;
}

.p-inputnumber-input {
  height: 52px;
  /* Used for the number fields in the conditionals modal of the Queries module */
}

/* Calendar */
.p-datepicker:not(.p-datepicker-inline) {
  background: #ffffff;
  border: 0 none;
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
  width: auto !important;
}

/* .p-fileupload.p-fileupload-advanced .p-message {
  margin-top: 10rem;
} */

/* .p-fileupload.p-fileupload-advanced .p-message {
  margin-top: 0;
  display: none;
} */
#typeahead-menu {
  top: 650px !important;
  z-index: 9999;
}

/*reviewDashboard*/
/* Comment this out if this causes any issues. - Yibran */
.p-datatable .p-datatable-thead > tr > th.reviewDashboard {
  background: white !important;
}

.p-multiselect {
  min-height: 50px;
  max-height: 50px;
  border: 1px solid #ced4da;
  padding: 6px 0px 0px 12px;
  font-weight: 600;
  font-size: 14px;
  font-family: 'Open Sans';
}

.p-dropdown {
  height: 50px;
  border: 1px solid #ced4da;
  border-radius: 5px;
}

.p-toast-message-content {
  display: flex;
  align-items: center;
}

.custom-lead .p-calendar .p-inputtext {
  padding: 10px;
  padding-left: 20px;
  font-size: 16px;
  border: 1px solid #5151511a !important;
  border-radius: 10px !important;
  color: #515151;
  font-weight: 600;
  font-family: 'Open Sans';
  height: 45px;
}
/* Nobody is using the Timeline component, so I removed the opposite side of the content for timeline.
 If needed, comment this out in case you are using the timeine component from prime react. */
.custom-timeline .p-timeline-event-opposite {
  flex-basis: 0;
  padding: 0;
  flex: none !important;
}

.custom-timeline .p-timeline-event {
  width: 100%;
}

/* Lexical editer Style */
.ltr {
  text-align: left;
}

.rtl {
  text-align: right;
}

.editor-text-underline {
  text-decoration: underline;
}

.editor-image img.focused.draggable {
  cursor: grab;
}

.editor-image img.focused.draggable:active {
  cursor: grabbing;
}

span.editor-image {
  cursor: default;
  display: inline-block;
  position: relative;
  user-select: none;
}

.editor-image img {
  max-width: 100%;
  cursor: default;
}

.editor-image img.focused {
  outline: 2px solid rgb(60, 132, 244);
  user-select: none;
}

.editor-image img.focused.draggable {
  cursor: grab;
}

.editor-image img.focused.draggable:active {
  cursor: grabbing;
}

.editor-image .image-caption-container .tree-view-output {
  margin: 0;
  border-radius: 0;
}

.editor-image .image-caption-container {
  display: block;
  position: absolute;
  bottom: 4px;
  left: 0;
  right: 0;
  padding: 0;
  margin: 0;
  border-top: 1px solid #fff;
  background-color: rgba(255, 255, 255, 0.9);
  min-width: 100px;
  color: #000;
  overflow: hidden;
}

.editor-image .image-caption-button {
  display: block;
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
  width: 30%;
  padding: 10px;
  margin: 0 auto;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 5px;
  background-color: rgba(0, 0, 0, 0.5);
  min-width: 100px;
  color: #fff;
  cursor: pointer;
  user-select: none;
}

.editor-image .image-caption-button:hover {
  background-color: rgba(60, 132, 244, 0.5);
}

.editor-image .image-edit-button {
  border: 1px solid rgba(0, 0, 0, 0.3);
  border-radius: 5px;
  background-image: url(/src/images/icons/pencil-fill.svg);
  background-size: 16px;
  background-position: center;
  background-repeat: no-repeat;
  width: 35px;
  height: 35px;
  vertical-align: -0.25em;
  position: absolute;
  right: 4px;
  top: 4px;
  cursor: pointer;
  user-select: none;
}

.editor-image .image-edit-button:hover {
  background-color: rgba(60, 132, 244, 0.1);
}

.editor-image .image-resizer {
  display: block;
  width: 7px;
  height: 7px;
  position: absolute;
  background-color: rgb(60, 132, 244);
  border: 1px solid #fff;
}

.editor-image .image-resizer.image-resizer-n {
  top: -6px;
  left: 48%;
  cursor: n-resize;
}

.editor-image .image-resizer.image-resizer-ne {
  top: -6px;
  right: -6px;
  cursor: ne-resize;
}

.editor-image .image-resizer.image-resizer-e {
  bottom: 48%;
  right: -6px;
  cursor: e-resize;
}

.editor-image .image-resizer.image-resizer-se {
  bottom: -2px;
  right: -6px;
  cursor: nwse-resize;
}

.editor-image .image-resizer.image-resizer-s {
  bottom: -2px;
  left: 48%;
  cursor: s-resize;
}

.editor-image .image-resizer.image-resizer-sw {
  bottom: -2px;
  left: -6px;
  cursor: sw-resize;
}

.editor-image .image-resizer.image-resizer-w {
  bottom: 48%;
  left: -6px;
  cursor: w-resize;
}

.editor-image .image-resizer.image-resizer-nw {
  top: -6px;
  left: -6px;
  cursor: nw-resize;
}

.editor-paragraph {
  padding: 0;
  margin: 0;
  position: relative;
}

.editor-paragraph:last-child {
  font-weight: 500;
  color: #002138;
}

.editor-quote {
  margin: 0;
  margin-left: 20px;
  font-size: 15px;
  color: rgb(101, 103, 107);
  border-left-color: rgb(206, 208, 212);
  border-left-width: 4px;
  border-left-style: solid;
  padding-left: 16px;
}

.editor-text-bold {
  font-weight: bold;
  color: #002138;
}

.editor-text-italic {
  font-style: italic;
}

.editor-text-strikethrough {
  text-decoration: line-through;
}

.editor-text-underlineStrikethrough {
  text-decoration: underline line-through;
}

.editor-code {
  background-color: rgb(240, 242, 245);
  font-family: Menlo, Consolas, Monaco, monospace;
  display: block;
  padding: 8px 8px 8px 52px;
  line-height: 1.53;
  font-size: 13px;
  margin: 0;
  margin-top: 8px;
  margin-bottom: 8px;
  tab-size: 2;
  /* white-space: pre; */
  overflow-x: auto;
  position: relative;
}

.editor-code:before {
  content: attr(data-gutter);
  position: absolute;
  background-color: #eee;
  left: 0;
  top: 0;
  border-right: 1px solid #ccc;
  padding: 8px;
  color: #777;
  white-space: pre-wrap;
  text-align: right;
  min-width: 25px;
}

.editor-code:after {
  content: attr(data-highlight-language);
  top: 0;
  right: 3px;
  padding: 3px;
  font-size: 10px;
  text-transform: uppercase;
  position: absolute;
  color: rgba(0, 0, 0, 0.5);
}

.editor-text-code {
  background-color: rgb(240, 242, 245);
  padding: 1px 0.25rem;
  font-family: Menlo, Consolas, Monaco, monospace;
  font-size: 94%;
}

.editor-link {
  color: rgb(33, 111, 219);
  text-decoration: none;
}

.editor-placeholder {
  color: #999;
  overflow: hidden;
  position: absolute;
  text-overflow: ellipsis;
  top: 15px;
  left: 10px;
  font-size: 15px;
  display: inline-block;
  pointer-events: none;
}

.editor-heading-h1 {
  font-size: 1.1rem;
  color: var(--primary-bg-blackPearl);
  font-weight: 700;
  margin: 0;
  margin-bottom: 12px;
  padding: 0;
}

.editor-heading-h2 {
  font-size: 15px;
  color: var(--primary-bg-blackPearl);
  font-weight: 700;
  margin: 0;
  margin-top: 10px;
  padding: 0;
  text-transform: uppercase;
}

.editor-list-ol {
  padding: 0;
  margin: 0;
  margin-left: 16px;
}

.editor-list-ul {
  padding: 0;
  margin: 0;
  margin-left: 16px;
}

.editor-listitem {
  margin: 8px 32px 8px 32px;
  color: #002138;
}

.editor-nested-listitem {
  list-style-type: none;
}

.editor-tokenComment {
  color: slategray;
}

.editor-tokenPunctuation {
  color: #999;
}

.editor-tokenProperty {
  color: #905;
}

.editor-tokenSelector {
  color: #690;
}

.editor-tokenOperator {
  color: #9a6e3a;
}

.editor-tokenAttr {
  color: #07a;
}

.editor-tokenVariable {
  color: #e90;
}

.editor-tokenFunction {
  color: #dd4a68;
}

.typeaheadPopover {
  top: 1000px;
  width: max-content;
  background-color: var(--primary-bg-darkCerulean-5);
  margin: 0;
  border-radius: 10px;
  box-shadow: 0 1px 20px 0 rgba(131, 131, 131, 0.1);
}

.typeaheadPopover > ul {
  list-style: none;
  white-space: nowrap;
  width: 100%;
  padding: 0.5rem 0.5rem;
  margin: 0;
  overflow-y: scroll;
  height: 200px;
}

.typeaheadPopover > ul > li {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  cursor: pointer;
  padding: 0.5rem 0.8rem;
  font-weight: bold;
}

.typeaheadPopover > ul > li:hover {
  background-color: var(--primary-bg-darkCerulean-10);
}

/* Lexical editer Style */

/* Group button Style */
.p-selectbutton > .p-button:hover {
  background-color: var(--primary-bg-blackPearl);
  color: #fff;
}

.p-highlight .p-focus {
  background: var(--primary-bg-blackPearl);
  border-color: var(--primary-bg-blackPearl);
  color: #fff;
}

/* .p-focus {
  background:  var(--primary-bg-blackPearl);
  border-color:  var(--primary-bg-blackPearl);
  color: #fff;
} */

/* Group button Style */

ul.p-listbox-list li {
  color: var(--primary-bg-darkCerulean-70);
  text-decoration: underline 1px var(--primary-bg-darkCerulean);
}

ul.p-listbox-list li:hover {
  text-decoration: underline 2px var(--primary-bg-darkCerulean);
}

#multiSelect .p-hidden-accessible {
  position: relative !important;
}

.custom-dropdownWid .p-inputtext {
  width: 143px;
}

.custom-dropdown-label .p-dropdown-label {
  max-width: 80%;
  display: block;
  white-space: nowrap;
  cursor: pointer;
  overflow: hidden;
  text-overflow: ellipsis;
}

.custom-multiselect-panel {
  width: 14vw;
}

.custom-fileupload-button .p-button.p-fileupload-choose {
  background-color: #e5edf2 !important;
  width: 65px !important;
  margin-left: 0 !important;
  box-shadow: none !important;
  padding-left: 1rem;
}

.custom-fileupload-button .p-button.p-fileupload-choose :focus {
  box-shadow: none !important;
}

.custom-fileupload .p-button.p-fileupload-choose {
  width: 3.5rem;
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  height: 50px;
  margin-left: 0;
  background-color: #f7f9fc !important;
  border: 1px solid #bcd0dd !important;
  font-family: 'Open Sans';
  font-weight: 600;
  color: #4a6f89;
  display: flex;
}

.custom-input .p-inputtext:enabled:focus {
  box-shadow: none !important;
  border-color: #cdd3d9 !important;
}

.custom-input .p-inputtext:enabled:hover {
  border-color: #cdd3d9 !important;
}

.custom-fileupload.button-expanded .p-button.p-fileupload-choose {
  width: 45.6vw;
  text-align: left;
}

.custom-fileupload.button-expanded .p-button-icon {
  color: #002138;
  border-right: 1px solid #bcd0dd;
  font-weight: bold;
  font-size: large;
  height: 50px;
  margin-left: -2%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3.5rem;
}

.p-multiselect-select-all-label {
  margin-left: 0.5rem;
  color: #004990;
  font-family: 'open sans';
  font-weight: 500;
}

.p-icon-field {
  position: relative;
  width: 100%;
}

.p-icon-field-right > .p-input-icon:last-of-type {
  right: 0.5rem;
  color: var(--primary-bg-darkCerulean);
}

.toggleContainer {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* jsSpreadsheet */
.customTogglePosition {
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: space-between;
}

.infoContainer {
  align-self: center;
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.customInfoContainer {
  align-self: center;
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.customRowInfoContainer {
  align-self: center;
  width: 36%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}

.custom-ratingStar .p-rating-item .p-rating-icon {
  font-size: 40px;
  width: 40px;
  height: 40px;
}

/* jsSpreadsheet */
:root {
  --jexcel-border-color: #000;
}

.jexcel_container {
  display: inline-block;
  padding-right: 2px;
  box-sizing: border-box;
  overscroll-behavior: contain;
  outline: none;
}

.jexcel_container.fullscreen {
  position: fixed;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  z-index: 21;
}

.jexcel_container.fullscreen .jexcel_content {
  overflow: auto;
  width: 100%;
  height: 100%;
  background-color: #ffffff;
}

.jexcel_container.with-toolbar .jexcel > thead > tr > td {
  top: 0;
}

.jexcel > thead > tr > td.mandatoryColumn {
  color: #d64550;
}

.jexcel_container.fullscreen.with-toolbar {
  height: calc(100% - 46px);
}

.jexcel_content {
  display: inline-block;
  box-sizing: border-box;
  padding-right: 3px;
  padding-bottom: 3px;
  position: relative;
  scrollbar-width: thin;
  scrollbar-color: #666 transparent;
}

@supports (-moz-appearance: none) {
  .jexcel_content {
    padding-right: 10px;
  }
}

.jexcel_content::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.jexcel_content::-webkit-scrollbar-track {
  background: #eee;
}

.jexcel_content::-webkit-scrollbar-thumb {
  background: #666;
}

.jexcel {
  border-collapse: separate;
  table-layout: fixed;
  white-space: nowrap;
  empty-cells: show;
  border: 0px;
  background-color: #fff;
  width: 0;

  border-top: 1px solid transparent;
  border-left: 1px solid transparent;
  border-right: 1px solid #ccc;
  border-bottom: 1px solid #ccc;
  font-family: 'Open Sans';
  font-size: 14px;
}

.jexcel > thead {
  font-weight: 600;
}

.jexcel > tfoot {
  font-weight: 600;
}

.jexcel > thead > tr > td {
  border-top: 1px solid #ccc;
  border-left: 1px solid #ccc;
  border-right: 1px solid transparent;
  border-bottom: 1px solid transparent;
  background-color: #f3f3f3;
  padding: 2px;
  cursor: pointer;
  box-sizing: border-box;
  overflow: hidden;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 2;
  font-weight: 700;
  color: #002138;
}

.jexcel_container.with-toolbar .jexcel > thead > tr > td {
  top: 42px;
}

.jexcel > thead > tr > td.dragging {
  background-color: #fff;
  opacity: 0.5;
}

.jexcel > thead > tr > td.selected {
  background-color: #dcdcdc;
}

.jexcel > thead > tr > td.arrow-up {
  background-repeat: no-repeat;
  background-position: center right 5px;
  background-image: url("data:image/svg+xml,%0A%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' d='M0 0h24v24H0V0z'/%3E%3Cpath d='M7 14l5-5 5 5H7z' fill='gray'/%3E%3C/svg%3E");
  text-decoration: underline;
}

.jexcel > thead > tr > td.arrow-down {
  background-repeat: no-repeat;
  background-position: center right 5px;
  background-image: url("data:image/svg+xml,%0A%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' d='M0 0h24v24H0V0z'/%3E%3Cpath d='M7 10l5 5 5-5H7z' fill='gray'/%3E%3C/svg%3E");
  text-decoration: underline;
}

.jexcel > tbody > tr > td:first-child {
  position: relative;
  background-color: #f3f3f3;
  text-align: center;
}

.jexcel > tbody.resizable > tr > td:first-child::before {
  content: '\00a0';
  width: 100%;
  height: 3px;
  position: absolute;
  bottom: 0px;
  left: 0px;
  cursor: row-resize;
}

.jexcel > tbody.draggable > tr > td:first-child::after {
  content: '\00a0';
  width: 3px;
  height: 100%;
  position: absolute;
  top: 0px;
  right: 0px;
  cursor: move;
}

.jexcel > tbody > tr.dragging > td {
  background-color: #eee;
  opacity: 0.5;
}

.jexcel > tbody > tr > td {
  border-top: 1px solid #ccc;
  border-left: 1px solid #ccc;
  border-right: 1px solid transparent;
  border-bottom: 1px solid transparent;
  padding: 4px;
  white-space: nowrap;
  box-sizing: border-box;
  line-height: 1em;
}

.jexcel_overflow > tbody > tr > td {
  overflow: hidden;
}

.jexcel > tbody > tr > td:last-child {
  overflow: hidden;
}

.jexcel > tbody > tr > td > img {
  display: inline-block;
  max-width: 100px;
}

.jexcel > tbody > tr > td.readonly {
  color: rgba(0, 0, 0, 0.3);
}

.jexcel > tbody > tr.selected > td:first-child {
  background-color: #dcdcdc;
}

.jexcel > tbody > tr > td > select,
.jexcel > tbody > tr > td > input,
.jexcel > tbody > tr > td > textarea {
  border: 0px;
  border-radius: 0px;
  outline: 0px;
  width: 100%;
  margin: 0px;
  padding: 0px;
  padding-right: 2px;
  background-color: transparent;
  box-sizing: border-box;
}

.jexcel > tbody > tr > td > textarea {
  resize: none;
  padding-top: 6px !important;
}

.jexcel > tbody > tr > td > input[type='checkbox'] {
  width: 12px;
  margin-top: 2px;
}

.jexcel > tbody > tr > td > input[type='radio'] {
  width: 12px;
  margin-top: 2px;
}

.jexcel > tbody > tr > td > select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-repeat: no-repeat;
  background-position-x: 100%;
  background-position-y: 40%;
  background-image: url(data:image/svg+xml;base64,PHN2ZyBmaWxsPSdibGFjaycgaGVpZ2h0PScyNCcgdmlld0JveD0nMCAwIDI0IDI0JyB3aWR0aD0nMjQnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zyc+PHBhdGggZD0nTTcgMTBsNSA1IDUtNXonLz48cGF0aCBkPSdNMCAwaDI0djI0SDB6JyBmaWxsPSdub25lJy8+PC9zdmc+);
}

.jexcel > tbody > tr > td.jexcel_dropdown {
  background-repeat: no-repeat;
  background-position: top 50% right 5px;
  background-image: url("data:image/svg+xml,%0A%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' d='M0 0h24v24H0V0z'/%3E%3Cpath d='M7 10l5 5 5-5H7z' fill='lightgray'/%3E%3C/svg%3E");
  text-overflow: ellipsis;
  overflow-x: hidden;
}

.jexcel > tbody > tr > td.jexcel_dropdown.jexcel_comments {
  background: url("data:image/svg+xml,%0A%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' d='M0 0h24v24H0V0z'/%3E%3Cpath d='M7 10l5 5 5-5H7z' fill='lightgray'/%3E%3C/svg%3E")
      top 50% right 5px no-repeat,
    url('data:image/png;base64,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')
      top right no-repeat;
}

.jexcel > tbody > tr > td > .color {
  width: 90%;
  height: 10px;
  margin: auto;
}

.jexcel > tbody > tr > td > a {
  text-decoration: underline;
}

.jexcel > tbody > tr > td.highlight > a {
  color: blue;
  cursor: pointer;
}

.jexcel > tfoot > tr > td {
  border-top: 1px solid #ccc;
  border-left: 1px solid #ccc;
  border-right: 1px solid transparent;
  border-bottom: 1px solid transparent;
  background-color: #f3f3f3;
  padding: 2px;
  cursor: pointer;
  box-sizing: border-box;
  overflow: hidden;
}

.jexcel .highlight {
  background-color: rgba(0, 0, 0, 0.05);
}

.jexcel .highlight-top {
  border-top: 1px solid #000;
  /* var(--jexcel-border-color);*/
  box-shadow: 0px -1px #ccc;
}

.jexcel .highlight-left {
  border-left: 1px solid #000;
  /* var(--jexcel-border-color);*/
  box-shadow: -1px 0px #ccc;
}

.jexcel .highlight-right {
  border-right: 1px solid #000;
  /* var(--jexcel-border-color);*/
}

.jexcel .highlight-bottom {
  border-bottom: 1px solid #000;
  /* var(--jexcel-border-color);*/
}

.jexcel .highlight-top.highlight-left {
  box-shadow: -1px -1px #ccc;
  -webkit-box-shadow: -1px -1px #ccc;
  -moz-box-shadow: -1px -1px #ccc;
}

.jexcel .highlight-selected {
  background-color: rgba(0, 0, 0, 0);
}

.jexcel .selection {
  background-color: rgba(0, 0, 0, 0.05);
}

.jexcel .selection-left {
  border-left: 1px dotted #000;
}

.jexcel .selection-right {
  border-right: 1px dotted #000;
}

.jexcel .selection-top {
  border-top: 1px dotted #000;
}

.jexcel .selection-bottom {
  border-bottom: 1px dotted #000;
}

.jexcel_corner {
  position: absolute;
  background-color: rgb(0, 0, 0);
  height: 1px;
  width: 1px;
  border: 1px solid rgb(255, 255, 255);
  top: -2000px;
  left: -2000px;
  cursor: crosshair;
  box-sizing: initial;
  z-index: 20;
  padding: 2px;
}

.jexcel .editor {
  outline: 0px solid transparent;
  overflow: visible;
  white-space: nowrap;
  text-align: left;
  padding: 0px;
  box-sizing: border-box;
  overflow: visible !important;
}

.jexcel .editor > input {
  padding-left: 4px;
}

.jexcel .editor .jupload {
  position: fixed;
  top: 100%;
  z-index: 40;
  -webkit-user-select: none;
  -webkit-font-smoothing: antialiased;
  font-size: 0.875rem;
  letter-spacing: 0.2px;
  -webkit-border-radius: 4px;
  border-radius: 4px;
  -webkit-box-shadow: 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12), 0 5px 5px -3px rgba(0, 0, 0, 0.2);
  box-shadow: 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12), 0 5px 5px -3px rgba(0, 0, 0, 0.2);
  padding: 10px;
  background-color: #fff;
  width: 300px;
  min-height: 225px;
  margin-top: 2px;
}

.jexcel .editor .jupload img {
  width: 100%;
  height: auto;
}

.jexcel .editor .jexcel_richtext {
  position: fixed;
  top: 100%;
  z-index: 40;
  -webkit-user-select: none;
  -webkit-font-smoothing: antialiased;
  font-size: 0.875rem;
  letter-spacing: 0.2px;
  -webkit-box-shadow: 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12), 0 5px 5px -3px rgba(0, 0, 0, 0.2);
  box-shadow: 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12), 0 5px 5px -3px rgba(0, 0, 0, 0.2);
  padding: 10px;
  background-color: #fff;
  min-width: 280px;
  max-width: 310px;
  margin-top: 2px;
  text-align: left;
}

.jexcel .editor .jclose:after {
  position: absolute;
  top: 0;
  right: 0;
  margin: 10px;
  content: 'close';
  font-family: 'Open Sans';
  font-size: 24px;
  width: 24px;
  height: 24px;
  line-height: 24px;
  cursor: pointer;
  text-shadow: 0px 0px 5px #fff;
}

.jexcel,
.jexcel td,
.jexcel_corner {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
}

.jexcel_textarea {
  position: absolute;
  top: -999px;
  left: -999px;
  width: 1px;
  height: 1px;
}

.jexcel .dragline {
  position: absolute;
}

.jexcel .dragline div {
  position: relative;
  top: -6px;
  height: 5px;
  width: 22px;
}

.jexcel .dragline div:hover {
  cursor: move;
}

.jexcel .onDrag {
  background-color: rgba(0, 0, 0, 0.6);
}

.jexcel .error {
  border: 1px solid red;
}

.jexcel thead td.resizing {
  border-right-style: dotted !important;
  border-right-color: red !important;
}

.jexcel tbody tr.resizing > td {
  border-bottom-style: dotted !important;
  border-bottom-color: red !important;
}

.jexcel tbody td.resizing {
  border-right-style: dotted !important;
  border-right-color: red !important;
}

.jexcel .jdropdown-header {
  border: 0px !important;
  outline: none !important;
  width: 100% !important;
  height: 100% !important;
  padding: 0px !important;
  padding-left: 8px !important;
}

.jexcel .jdropdown-container {
  margin-top: 1px;
}

.jexcel .jdropdown-container-header {
  padding: 0px;
  margin: 0px;
  height: inherit;
}

.jexcel .jdropdown-picker {
  border: 0px !important;
  padding: 0px !important;
  width: inherit;
  height: inherit;
}

.jexcel .jexcel_comments {
  background: url('data:image/png;base64,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');
  background-repeat: no-repeat;
  background-position: top right;
}

.jexcel .sp-replacer {
  margin: 2px;
  border: 0px;
}

.jexcel > thead > tr.jexcel_filter > td > input {
  border: 0px;
  width: 100%;
  outline: none;
}

.jexcel_about {
  float: right;
  font-size: 0.7em;
  padding: 2px;
  text-transform: uppercase;
  letter-spacing: 1px;
  display: none;
}

.jexcel_about a {
  color: #ccc;
  text-decoration: none;
}

.jexcel_about img {
  display: none;
}

.jexcel_filter {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.jexcel_filter > div {
  padding: 8px;
  align-items: center;
}

.jexcel_pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.jexcel_pagination > div {
  display: flex;
  padding: 10px;
}

.jexcel_pagination > div:last-child {
  padding-right: 10px;
  padding-top: 10px;
}

.jexcel_pagination > div > div {
  text-align: center;
  width: 36px;
  height: 36px;
  line-height: 34px;
  border: 1px solid #ccc;
  box-sizing: border-box;
  margin-left: 2px;
  cursor: pointer;
}

.jexcel_page {
  font-size: 0.8em;
}

.jexcel_page_selected {
  font-weight: bold;
  background-color: #f3f3f3;
}

.jexcel_toolbar {
  display: flex;
  background-color: #f3f3f3;
  border: 1px solid #ccc;
  padding: 4px;
  margin: 0px 2px 4px 1px;
  position: sticky;
  top: 0px;
  z-index: 21;
}

.jexcel_toolbar:empty {
  display: none;
}

.jexcel_toolbar i.jexcel_toolbar_item {
  width: 24px;
  height: 24px;
  padding: 4px;
  cursor: pointer;
  display: inline-block;
}

.jexcel_toolbar i.jexcel_toolbar_item:hover {
  background-color: #ddd;
}

.jexcel_toolbar select.jexcel_toolbar_item {
  margin-left: 2px;
  margin-right: 2px;
  display: inline-block;
  border: 0px;
  background-color: transparent;
  padding-right: 10px;
}

.jexcel .dragging-left {
  background-repeat: no-repeat;
  background-position: top 50% left 0px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M14 7l-5 5 5 5V7z'/%3E%3Cpath fill='none' d='M24 0v24H0V0h24z'/%3E%3C/svg%3E");
}

.jexcel .dragging-right {
  background-repeat: no-repeat;
  background-position: top 50% right 0px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M10 17l5-5-5-5v10z'/%3E%3Cpath fill='none' d='M0 24V0h24v24H0z'/%3E%3C/svg%3E");
}

.jexcel_tabs .jexcel_tab {
  display: none;
}

.jexcel_tabs .jexcel_tab_link {
  display: inline-block;
  padding: 10px;
  padding-left: 20px;
  padding-right: 20px;
  margin-right: 5px;
  margin-bottom: 5px;
  background-color: #f3f3f3;
  cursor: pointer;
}

.jexcel_tabs .jexcel_tab_link.selected {
  background-color: #ddd;
}

.jexcel_hidden_index > tbody > tr > td:first-child,
.jexcel_hidden_index > thead > tr > td:first-child,
.jexcel_hidden_index > tfoot > tr > td:first-child,
.jexcel_hidden_index > colgroup > col:first-child {
  display: none;
}

.jexcel .jrating {
  display: inline-flex;
}

.jexcel .jrating > div {
  zoom: 0.55;
}

.jexcel .copying-top {
  border-top: 1px dashed #000;
}

.jexcel .copying-left {
  border-left: 1px dashed #000;
}

.jexcel .copying-right {
  border-right: 1px dashed #000;
}

.jexcel .copying-bottom {
  border-bottom: 1px dashed #000;
}

.jexcel .jexcel_column_filter {
  background-repeat: no-repeat;
  background-position: top 50% right 5px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='gray' width='18px' height='18px'%3E%3Cpath d='M10 18h4v-2h-4v2zM3 6v2h18V6H3zm3 7h12v-2H6v2z'/%3E%3Cpath d='M0 0h24v24H0z' fill='none'/%3E%3C/svg%3E");
  text-overflow: ellipsis;
  overflow: hidden;
  padding: 0px;
  padding-left: 6px;
  padding-right: 20px;
}

.jexcel thead .jexcel_freezed,
.jexcel tfoot .jexcel_freezed {
  left: 0px;
  z-index: 3 !important;
  box-shadow: 2px 0px 2px 0.2px #ccc !important;
  -webkit-box-shadow: 2px 0px 2px 0.2px #ccc !important;
  -moz-box-shadow: 2px 0px 2px 0.2px #ccc !important;
}

.jexcel tbody .jexcel_freezed {
  position: relative;
  background-color: #fff;
  box-shadow: 1px 1px 1px 1px #ccc !important;
  -webkit-box-shadow: 2px 4px 4px 0.1px #ccc !important;
  -moz-box-shadow: 2px 4px 4px 0.1px #ccc !important;
}

.red {
  color: red;
}

.jexcel > tbody > tr > td.readonly > input[type='checkbox'],
.jexcel > tbody > tr > td.readonly > input[type='radio'] {
  pointer-events: none;
  opacity: 0.5;
}

.p-button .p-button-label {
  font-family: 'Open Sans';
}

/* jsSpreadsheet */

.formbuilder_Input {
  min-height: 43px;
  max-height: 43px;
  padding: 0;
}

.formbuilder_numInput.p-inputnumber-input {
  min-height: 43px;
  max-height: 43px;
  padding: 0;
}

.settingsHover {
  padding: 8px 14px;
  font-size: 22px;
}

.settingsHover:hover {
  padding: 8px 14px;
  background-color: var(--primary-bg-blackPearl-10);
}

.custom-rating .p-rating-item .p-rating-icon.p-icon {
  width: 26px;
  height: 26px;
}

.elementSpace {
  padding: 20px 20px;
}

.Sortable_hoverContainer__PJstc:hover i {
  color: #024f7c !important;
}

/* lead generation */

.custom-lead .p-datatable-header {
  padding: 0 !important;
}
.custom-lead .p-datatable-thead > tr > th {
  text-align: left;
  font-weight: 700;
  font-family: 'Open Sans';
  font-size: 22px;
  padding: 1.25rem 1rem;
  border-width: 0 0 1px 0 !important;
  color: #515151 !important;
  background: #fff !important;
  transition: box-shadow 0.2s !important;
}
.custom-lead .p-datatable-tbody > tr {
  transition: box-shadow 0.2s;
  font-size: 12px !important;
  font-weight: 700;
  color: #002138 !important;
  height: 48px;
  background-color: #fff !important;
}

.custom-lead .p-row-odd {
  background-color: #fff !important;
}
.custom-lead .p-row-even {
  background-color: #fff !important;
}

/* Right border for all cells */
.custom-lead .p-datatable-thead > tr > th,
.custom-lead .p-datatable-tbody > tr > td {
  border-right: 1px solid #5151511a !important;
  border-bottom: 1px solid #5151511a !important;
  color: #515151;
}

/* Left border only for the first column */
.custom-lead .p-datatable-thead > tr > th:first-child,
.custom-lead .p-datatable-tbody > tr > td:first-child {
  border-left: 1px solid #5151511a !important;
  color: #515151;
}

.custom-lead .p-inputtext {
  padding: 10px;
  font-size: 16px;
  border: 1px solid #5151511a;
  /* border-radius: 10px; */
  color: #515151 !important;
  font-weight: 600;
  font-family: 'Open Sans';
  height: 45px;
}
.custom-lead .p-dropdown-item-label {
  color: #515151;
}
.custom-lead .p-multiselect-item {
  color: #515151 !important;
  border-bottom: 1px solid #5151511a;
  padding: 10px;
}
.custom-lead .p-multiselect-item:last-child {
  border-bottom: none;
}
.custom-lead .p-multiselect-item:hover {
  background-color: #1b84ff0d;
  border-radius: 10px;
}
.custom-lead .p-dropdown-trigger {
  color: #51515133 !important;
}
.custom-lead .p-multiselect-trigger {
  color: #51515133 !important;
}
.custom-lead.p-dropdown-panel .p-dropdown-items .p-dropdown-item {
  border-bottom: 1px solid #5151511a;
}

.custom-lead.p-dropdown-panel .p-dropdown-items .p-dropdown-item:hover {
  background-color: #1b84ff0d;
  border-radius: 10px;
}
.custom-lead.p-dropdown-panel .p-dropdown-items .p-dropdown-item:last-child {
  border-bottom: none;
}
.custom-lead.p-dropdown-panel .p-dropdown-items .p-multiselect-item {
  border-bottom: 1px solid #5151511a;
}

.custom-lead.p-dropdown-panel {
  box-shadow: none;
  border-radius: 10px;
  border: 1px solid #5151511a;
}
.custom-lead.p-dropdown .p-dropdown-label {
  border: none !important;
}

.custom-lead.p-dropdown .p-dropdown-label.p-placeholder {
  color: #ccd2d7 !important;
}

.custom-lead.p-inputgroup-addon {
  background-color: #1b84ff0d !important;
  border: 1px solid #5151511a;
}

.custom-lead.p-multiselect-panel {
  box-shadow: none;
  border-radius: 10px;
  border: 1px solid #5151511a;
}

.custom-lead .p-dialog-header {
  background-color: #1b84ff0d;
  color: #515151;
  padding: 1rem;
  border-top-right-radius: 10px;
  border-top-left-radius: 10px;
  border: 1px solid #5151511a;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.custom-lead.p-dialog .p-dialog-header .p-dialog-header-icon {
  background: none !important;
}

.custom-lead.p-calendar .p-inputtext {
  height: 45px;
  border-radius: 10px;
  padding-left: 20px;
  box-shadow: none;
}
.custom-lead.p-datepicker-calendar-container [data-pc-section='weekday'] {
  color: #000;
  font-family: 'Open Sans';
}
.custom-lead.p-datepicker-calendar-container [data-pc-section='daylabel'] {
  color: #515151;
  font-family: 'Open Sans';
}
.custom-lead.p-datepicker table td.p-datepicker-today > span {
  background-color: #1b84ff;
  color: #fff;
  font-family: 'Open Sans';
}
.custom-lead.p-datepicker:not(.p-disabled) table td span:not(.p-highlight):not(.p-disabled):hover {
  background-color: #1b84ff;
  color: #fff;
  font-family: 'Open Sans';
}
.custom-lead.p-datepicker:not(.p-datepicker-inline) {
  border-radius: 10px;
  box-shadow: 5px 5px 20px rgba(81, 81, 81, 0.1);
  font-family: 'Open Sans';
}
.custom-lead.p-datepicker .p-datepicker-header .p-datepicker-title .p-datepicker-month {
  color: #828282;
  font-size: large;
  font-family: 'Open Sans';
}
.custom-lead.p-datepicker .p-datepicker-header .p-datepicker-title .p-datepicker-year {
  color: #828282;
  font-size: large;
  font-family: 'Open Sans';
}
.custom-lead.p-breadcrumb .p-breadcrumb-list li.p-menuitem-separator {
  color: #515151;
}

/* toast error */
.custom-lead.p-toast .p-toast-message.p-toast-message-error {
  width: 18vw;
  border: 1px solid #f8285a;
  border-radius: 10px;
  background-color: #fed4de;
  box-shadow: none;
}
.custom-lead.p-toast .p-toast-summary {
  color: #f8285a;
}
.custom-lead.p-toast .p-toast-detail {
  color: #f8285a;
  margin: 0;
}

/* toast success */
.custom-lead.p-toast .p-toast-message.p-toast-message-success {
  width: 18vw;
  border: 1px solid #04cb12;
  border-radius: 10px;
  background-color: #eafff1;
  box-shadow: none;
}
.custom-lead.p-toast .p-toast-message.p-toast-message-success .p-toast-summary {
  color: #04cb12;
}
.custom-lead.p-toast .p-toast-message.p-toast-message-success .p-toast-detail {
  color: #04cb12;
}

/* toast warning */

.custom-lead.p-toast .p-toast-message.p-toast-message-warn {
  width: 18vw;
  border: 1px solid #ffde05;
  border-radius: 10px;
  background-color: #fffdef;
  box-shadow: none;
}
.custom-lead.p-toast .p-toast-message.p-toast-message-warn .p-toast-summary {
  color: #ffde05;
}
.custom-lead.p-toast .p-toast-message.p-toast-message-warn .p-toast-detail {
  color: #ffde05;
}

/* toast info */

.custom-lead.p-toast .p-toast-message.p-toast-message-info {
  width: 18vw;
  border: 1px solid #1b84ff;
  border-radius: 10px;
  background-color: #eff6ff;
  box-shadow: none;
}
.custom-lead.p-toast .p-toast-message.p-toast-message-info .p-toast-summary {
  color: #1b84ff;
}
.custom-lead.p-toast .p-toast-message.p-toast-message-info .p-toast-detail {
  color: #1b84ff;
}

.custom-lead.p-inputswitch.p-highlight .p-inputswitch-slider {
  background-color: #1b84ff !important;
}
.custom-choose-btn.p-button.p-fileupload-choose {
  border: none;
  background-color: #1b84ff;
  height: 45px;
  padding: 10px 20px;
  font-size: 16px;
  border-radius: 10px;
  color: #fff;
  cursor: pointer;
  font-family: 'Open Sans';
  font-weight: 600;
  width: auto;
}
.custom-choose-btn-secondary.p-button.p-fileupload-choose {
  border: none;
  background-color: #e7f1fd;
  height: 45px;
  padding: 10px 20px;
  font-size: 16px;
  border-radius: 10px;
  color: #515151;
  cursor: pointer;
  font-family: 'Open Sans';
  font-weight: 600;
  border: 1px solid #1b84ff;
  width: auto;
}
.hide-button .p-button-label.p-clickable {
  display: none;
}
/* PdfViewer.module.css */
.react-pdf__Page__textContent {
  display: none !important; /* Hides the text layer completely */
}

.loader {
  width: 50px;
  aspect-ratio: 1;
  display: grid;
  border-radius: 50%;
  background: linear-gradient(0deg, rgb(0 0 0/50%) 30%, #0000 0 70%, rgb(0 0 0/100%) 0) 50%/8% 100%,
    linear-gradient(90deg, rgb(0 0 0/25%) 30%, #0000 0 70%, rgb(0 0 0/75%) 0) 50%/100% 8%;
  background-repeat: no-repeat;
  animation: l23 1s infinite steps(12);
}
.loader::before,
.loader::after {
  content: '';
  grid-area: 1/1;
  border-radius: 50%;
  background: inherit;
  opacity: 0.915;
  transform: rotate(30deg);
}
.loader::after {
  opacity: 0.83;
  transform: rotate(60deg);
}
@keyframes l23 {
  100% {
    transform: rotate(1turn);
  }
}

.go695645437 {
  box-shadow: none !important;
}

.tui-calendar-weekday-grid .tui-calendar-weekday-grid-header {
  height: 60px !important;
  /* line-height: 60px !important; */
  font-size: 14px;
  background-color: #f5f5f5; /* optional */
}

.metronicLabel {
  font-size: 16px !important;
  font-weight: 600;
  color: #515151 !important;
}

.metronicCalendarInput {
  font-size: 16px;
  border-radius: 10px;
  color: #515151;
  font-weight: 600;
  font-family: 'Open Sans';
  height: 45px;
}
